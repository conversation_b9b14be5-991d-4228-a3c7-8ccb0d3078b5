import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Entity<PERSON>anager, In, QueryRunner, Repository } from 'typeorm';
import { MessageThread } from '../entities/message-thread.entity';
import { UpdateMessageThreadDto } from '../dto/message-thread.dto';
import { SyncStateService } from './sync-state.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';

@Injectable()
export class MessageThreadService {
  constructor(
    @InjectRepository(MessageThread)
    private readonly messageThreadRepo: Repository<MessageThread>,
    private readonly syncStateService: SyncStateService,
  ) {}

  async findOrCreateThreadWithTransaction(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    queryRunner: QueryRunner,
    welcomeMessage?: string,
    lastMessageId?: number,
  ): Promise<MessageThread> {
    let thread = await queryRunner.manager.findOne(MessageThread, {
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });

    if (!thread) {
      thread = queryRunner.manager.create(MessageThread, {
        memberId,
        chatType,
        targetId,
        unreadCount: 0,
        isMuted: false,
        isPinned: false,
        welcomeMessage,
        lastMessageId,
        lastMessageAt: lastMessageId ? new Date() : null,
        canSendMessages: chatType === ChatType.GROUP ? true : false,
      });

      thread = await queryRunner.manager.save(MessageThread, thread);

      // Create sync state within transaction
      await this.syncStateService.createSyncStateWithTransaction(
        thread.id,
        queryRunner,
      );
    }

    return thread;
  }

  async findOrCreateThread(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<MessageThread> {
    let thread = await this.messageThreadRepo.findOne({
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });

    if (!thread) {
      thread = this.messageThreadRepo.create({
        memberId,
        chatType,
        targetId,
        unreadCount: 0,
        isMuted: false,
        isPinned: false,
      });
      thread = await this.messageThreadRepo.save(thread);
      await this.syncStateService.createSyncState(thread.id);
    }

    return thread;
  }

  async updateThreadAfterMessage(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    messageId: number,
    messageSeq: number,
    isFromSelf: boolean = false,
  ): Promise<MessageThread> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    // Update last message info
    thread.lastMessageId = messageId;

    // Only increment unread count if message is not from the member themselves
    if (!isFromSelf) {
      thread.unreadCount += 1;
    }

    // Mark sync state as having pending messages if not from self
    if (thread.syncState) {
      thread.syncState.hasPending = !isFromSelf;
      // Update lastSyncedSeq to current message sequence
      thread.syncState.lastSyncedSeq = messageSeq;
    }

    return await this.messageThreadRepo.save(thread);
  }

  async updateUnreadCount(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    increment: number,
  ): Promise<void> {
    await this.messageThreadRepo
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        unreadCount: () => `unread_count + ${increment}`,
        updatedAt: new Date(),
      })
      .where(
        'member_id = :memberId AND chat_type = :chatType AND target_id = :targetId',
        {
          memberId,
          chatType,
          targetId,
        },
      )
      .execute();
  }

  async markAsRead(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    lastReadMessageSeq?: number,
  ): Promise<void> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    thread.unreadCount = 0;

    if (thread.syncState && lastReadMessageSeq) {
      thread.syncState.lastAckedSeq = lastReadMessageSeq;
      thread.syncState.hasPending = false;
    }

    await this.messageThreadRepo.save(thread);
  }

  async updateThreadsBySession(
    manager: EntityManager,
    sessionId: string,
    updates: Partial<MessageThread>,
  ): Promise<void> {
    await manager.update(
      MessageThread,
      { sessionId },
      {
        ...updates,
      },
    );
  }

  async updatePrivateThreadMessagingPermission(
    member1Id: number,
    member2Id: number,
    canSendMessages: boolean,
    manager: EntityManager,
  ): Promise<void> {
    // Update thread for member1 -> member2 (PRIVATE chat only)
    await manager
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        canSendMessages,
        updatedAt: new Date(),
      })
      .where('memberId = :member1Id', { member1Id })
      .andWhere('chatType = :chatType', { chatType: ChatType.PRIVATE })
      .andWhere('targetId = :member2Id', { member2Id })
      .execute();

    // Update thread for member2 -> member1 (PRIVATE chat only)
    await manager
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        canSendMessages,
        updatedAt: new Date(),
      })
      .where('memberId = :member2Id', { member2Id })
      .andWhere('chatType = :chatType', { chatType: ChatType.PRIVATE })
      .andWhere('targetId = :member1Id', { member1Id })
      .execute();
  }

  async linkThreadsWithSession(
    member1Id: number,
    member2Id: number,
    sessionId: string,
    isActive: boolean,
    manager: EntityManager,
  ): Promise<void> {
    // Find threads for both members
    const thread1 = await manager.findOne(MessageThread, {
      where: {
        memberId: member1Id,
        chatType: ChatType.PRIVATE,
        targetId: member2Id,
      },
    });

    const thread2 = await manager.findOne(MessageThread, {
      where: {
        memberId: member2Id,
        chatType: ChatType.PRIVATE,
        targetId: member1Id,
      },
    });

    // Link both threads with session
    if (thread1) {
      await this.updateLinkThreadWithSession(
        thread1.id,
        sessionId,
        isActive,
        manager,
      );
    }

    if (thread2) {
      await this.updateLinkThreadWithSession(
        thread2.id,
        sessionId,
        isActive,
        manager,
      );
    }
  }

  // New method to link thread with session
  async updateLinkThreadWithSession(
    threadId: number,
    sessionId: string,
    isActive: boolean,
    manager: EntityManager,
  ): Promise<void> {
    await manager.update(MessageThread, threadId, {
      sessionId,
      canSendMessages: isActive,
    });
  }

  // New method to get threads by session status
  async getMemberThreads(
    memberId: number,
    chatType: ChatType,
    canSendMessages?: boolean,
  ): Promise<MessageThread[]> {
    const queryBuilder = this.messageThreadRepo
      .createQueryBuilder('thread')
      .leftJoinAndSelect('thread.syncState', 'syncState')
      .where('thread.memberId = :memberId', { memberId })
      .andWhere('thread.chatType = :chatType', { chatType });

    if (canSendMessages !== undefined) {
      queryBuilder.andWhere('thread.canSendMessages = :canSendMessages', {
        canSendMessages,
      });
    }

    return queryBuilder.orderBy('thread.updatedAt', 'DESC').getMany();
  }

  // New method to get thread by session
  async getThreadBySession(
    memberId: number,
    sessionId: string,
  ): Promise<MessageThread | null> {
    return this.messageThreadRepo.findOne({
      where: {
        memberId,
        sessionId,
      },
      relations: ['syncState'],
    });
  }

  // New method to update thread session status
  async updateThreadSessionStatus(
    threadId: number,
    isActive: boolean,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(MessageThread)
      : this.messageThreadRepo;

    await repository.update(threadId, {
      canSendMessages: isActive,
    });
  }

  // New method for bulk session updates
  async bulkUpdateThreadSessions(
    updates: Array<{
      threadId: number;
      sessionId?: string;
      canSendMessages: boolean;
    }>,
    manager: EntityManager,
  ): Promise<void> {
    for (const update of updates) {
      await manager.update(MessageThread, update.threadId, {
        sessionId: update.sessionId,
        canSendMessages: update.canSendMessages,
      });
    }
  }

  // New method to validate thread access
  async canUserAccessThread(
    memberId: number,
    threadId: number,
  ): Promise<boolean> {
    const thread = await this.messageThreadRepo.findOne({
      where: { id: threadId, memberId },
    });

    if (!thread) return false;

    return false;
  }

  async updateThreadSettings(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    settings: UpdateMessageThreadDto,
  ): Promise<MessageThread> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    if (settings.isMuted !== undefined) {
      thread.isMuted = settings.isMuted;
    }

    if (settings.isPinned !== undefined) {
      thread.isPinned = settings.isPinned;
    }

    return await this.messageThreadRepo.save(thread);
  }

  async deleteThread(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<void> {
    await this.messageThreadRepo.delete({
      memberId,
      chatType,
      targetId,
    });
  }

  async getThreadById(id: number): Promise<MessageThread> {
    const thread = await this.messageThreadRepo.findOne({
      where: { id },
      relations: ['syncState'],
    });

    if (!thread) {
      throw new NotFoundException(`Thread with id ${id} not found`);
    }

    return thread;
  }

  async getThreadByTargetId(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<MessageThread | null> {
    return this.messageThreadRepo.findOne({
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });
  }

  async bulkUpdateThreadsForMessage(
    memberIds: number[],
    chatType: ChatType,
    targetId: number,
    messageId: number,
    // messageSeq: number,
    senderId: number,
  ): Promise<void> {
    if (memberIds.length === 0) return;

    const existingThreads = await this.messageThreadRepo.find({
      where: {
        memberId: In(memberIds),
        chatType,
        targetId,
      },
      relations: ['syncState'],
    });

    const existingMemberIds = existingThreads.map((t) => t.memberId);
    const missingMemberIds = memberIds.filter(
      (id) => !existingMemberIds.includes(id),
    );

    for (const memberId of missingMemberIds) {
      await this.findOrCreateThread(memberId, chatType, targetId);
    }

    // update threads
    await this.messageThreadRepo
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        lastMessageId: messageId,
        unreadCount: () =>
          `CASE WHEN member_id = ${senderId} THEN unread_count ELSE unread_count + 1 END`,
        updatedAt: new Date(),
      })
      .where(
        'chat_type = :chatType AND target_id = :targetId AND member_id IN (:...memberIds)',
        { chatType, targetId, memberIds },
      )
      .execute();

    // update sync states via service
    for (const thread of existingThreads) {
      await this.syncStateService.updateSyncState(thread.id, {
        // lastSyncedSeq: messageSeq,
        hasPending: thread.memberId === senderId ? false : true,
      });
    }
  }
}
