import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoreModule } from 'src/core/core.module';
import { SecurityModule } from 'src/modules/security/security.module';
import { PairChat } from './entities/pair-chats.entity';
import { GroupUserService } from './services/group-user.service';
import { GroupsModule } from 'src/modules/groups/groups.module';
import { AuditModule } from 'src/modules/audit/audit.module';
import { MembersModule } from 'src/modules/members/members.module';
import { UserGroupsController } from './controllers/user-groups.controller';
import { MessagesModule } from 'src/modules/messages/messages.module';
import { PairChatService } from './services/pair-chat.service';
import { GroupNotificationService } from './services/group-notification.service';
import { InfrastructureModule } from 'src/infrastructure/infrastructure.module';
import { SessionManagementService } from './services/session-management.service';
import { MemberEventsHandler } from './handlers/memberVerifiedHandler';

/**
 * P2P Module
 *
 * Handles peer-to-peer messaging functionality including
 * end-to-end encryption, key exchange, session establishment,
 * and secure messaging using Signal Protocol.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([PairChat]),
    SecurityModule,
    CoreModule,
    MembersModule,
    GroupsModule,
    AuditModule,
    MessagesModule,
    InfrastructureModule,
  ],
  controllers: [UserGroupsController],
  providers: [
    GroupUserService,
    PairChatService,
    GroupNotificationService,
    SessionManagementService,
    MemberEventsHandler,
  ],
  exports: [SessionManagementService],
})
export class P2pModule {}
