import { PairChat } from 'src/modules/chat/p2p/entities/pair-chats.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { MessageThread } from 'src/modules/messages/entities/message-thread.entity';

@Entity('session_states')
@Index(['pairChatId'], { unique: true })
export class SessionStateEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  pairChatId: number;

  @Column('jsonb', { nullable: true })
  groupContextIds: number[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  expiredAt?: Date;

  @Column({ nullable: true, type: 'timestamp' })
  lastActiveAt?: Date;

  @Column({ default: false })
  wasReactivated: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  deactivatedAt?: Date | null;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => PairChat, (pairChat) => pairChat.sessions)
  @JoinColumn({ name: 'pairChatId' })
  pairChat: PairChat;

  @OneToMany(() => MessageThread, (thread) => thread.session)
  threads: MessageThread[];
}
