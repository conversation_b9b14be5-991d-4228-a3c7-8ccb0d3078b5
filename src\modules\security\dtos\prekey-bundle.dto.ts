import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsDateString,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { OneTimePreKeyDto } from './onetime-prekey.dto';
import { Type } from 'class-transformer';

export class CreatePreKeyBundleDto {
  @ApiProperty({ description: 'Unique keyId for signed prekey' })
  @IsNumber()
  keyId: number;

  @ApiProperty({ description: 'Public key for signed prekey' })
  @IsString()
  publicKey: string;

  @ApiProperty({ description: 'Signature of signed prekey' })
  @IsString()
  signature: string;

  @ApiProperty({ description: 'Expiration date of signed prekey' })
  @IsDateString()
  expireAt: string;

  @ApiProperty({
    type: [OneTimePreKeyDto],
    description: 'Array of one-time prekeys',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OneTimePreKeyDto)
  oneTimePreKeys: OneTimePreKeyDto[];
}
