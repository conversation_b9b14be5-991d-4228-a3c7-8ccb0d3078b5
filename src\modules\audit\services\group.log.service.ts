import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import {
  GroupChangeType,
  GroupLogAction,
} from '../enums/group-log-action.enum';
import { GroupChangeLog } from '../entities/group-change-logs.entity';

@Injectable()
export class GroupLogService {
  constructor(
    @InjectRepository(GroupChangeLog)
    private readonly groupLogRepo: Repository<GroupChangeLog>,
  ) {}

  // 🔹 For group join/leave/key changes
  async logChange(
    groupId: number,
    changeType: GroupChangeType,
    action: GroupLogAction,
    options?: {
      memberId?: number;
      changedField?: string;
      oldValue?: string;
      newValue?: string;
    },
  ): Promise<number> {
    // get max seq for this group
    const maxSeqResult = await this.groupLogRepo
      .createQueryBuilder('log')
      .select('MAX(log.groupSeq)', 'maxSeq')
      .where('log.groupId = :groupId', { groupId })
      .getRawOne();

    const nextSeq = (parseInt(maxSeqResult?.maxSeq, 10) || 0) + 1;

    const logEntry = this.groupLogRepo.create({
      groupSeq: nextSeq,
      groupId,
      changeType,
      action,
      memberId: options?.memberId,
      changedField: options?.changedField,
      oldValue: options?.oldValue,
      newValue: options?.newValue,
    });

    await this.groupLogRepo.save(logEntry);
    return nextSeq;
  }

  async logBatchKeyChanges(
    groupId: number,
    memberIds: number[],
    oldKeyVersion: number,
    newKeyVersion: number,
    queryRunner: QueryRunner,
  ): Promise<number> {
    const manager = queryRunner.manager;

    try {
      // ✅ Get current max sequence
      const maxSeqResult = await manager
        .createQueryBuilder()
        .select('MAX(log.group_seq)', 'maxSeq')
        .from(GroupChangeLog, 'log')
        .where('log.group_id = :groupId', { groupId })
        .getRawOne();

      let nextSeq = (parseInt(maxSeqResult?.maxSeq, 10) || 0) + 1;

      // ✅ Build log entries
      const logEntries = memberIds.map((memberId) => {
        const entry = {
          groupSeq: nextSeq++,
          groupId,
          changeType: GroupChangeType.KEY,
          action: GroupLogAction.KEY_UPDATE,
          memberId,
          changedField: 'key_version',
          oldValue: oldKeyVersion.toString(),
          newValue: newKeyVersion.toString(),
          createdAt: new Date(),
        };
        return entry;
      });

      // ✅ Insert logs
      if (logEntries.length > 0) {
        await manager.insert(GroupChangeLog, logEntries);
        return logEntries[logEntries.length - 1].groupSeq;
      }

      return maxSeqResult?.maxSeq || 0;
    } catch (error) {
      console.error('Batch key logging failed:', error);
      throw error;
    }
  }

  // 🔹 For logging member profile updates
  // async logMemberUpdate(
  //   memberId: number,
  //   changedField: string,
  //   oldValue: string,
  //   newValue: string,
  //   manager?: EntityManager,
  // ) {
  //   const repo = manager
  //     ? manager.getRepository(GroupChangeLog)
  //     : this.groupLogRepo;

  //   const maxSeq = await repo
  //     .createQueryBuilder('log')
  //     .select('MAX(log.seq)', 'maxSeq')
  //     .where('log.memberId = :memberId', { memberId })
  //     .getRawOne();

  //   const nextSeq = (parseInt(maxSeq?.maxSeq, 10) || 0) + 1;

  //   const logEntry = repo.create({
  //     seq: nextSeq,
  //     changeType: GroupChangeType.PROFILE,
  //     action: GroupLogAction.PROFILE_UPDATE,
  //     memberId,
  //     changedField,
  //     oldValue,
  //     newValue,
  //   });

  //   return repo.save(logEntry);
  // }
}
