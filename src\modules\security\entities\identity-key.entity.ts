import { OrgMember } from '../../../modules/members/entities/org-member.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  JoinColumn,
  OneToOne,
} from 'typeorm';
export interface EncryptedPrivateKey {
  encryptedData: string;
  nonce: string;
  salt: string;
}
@Entity('identity_keys')
@Index(['memberId'], { unique: true })
export class IdentityKeyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  memberId: number;

  @Column('text', { nullable: true })
  publicKey: string;

  @Column({ type: 'jsonb', nullable: true })
  encryptedPrivateKey: EncryptedPrivateKey;

  @CreateDateColumn({ name: 'created_at' })
  keyCreatedAt: Date;

  @OneToOne(() => OrgMember, (member) => member.identityKey, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'memberId' })
  member: OrgMember;
}
