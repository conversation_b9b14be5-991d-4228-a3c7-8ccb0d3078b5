import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Group } from '../../groups/entities/group.entity';
import {
  GroupChangeType,
  GroupLogAction,
} from '../enums/group-log-action.enum';
import { OrgMember } from 'src/modules/members/entities/org-member.entity';

@Entity('group_change_logs')
@Index('idx_group_seq', ['groupId', 'groupSeq'], { unique: true })
export class GroupChangeLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'member_id', nullable: true })
  memberId: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ type: 'bigint', name: 'group_seq' })
  groupSeq: number;

  @Column({
    name: 'change_type',
    type: 'enum',
    enum: GroupChangeType,
  })
  changeType: GroupChangeType;

  @Column({
    type: 'enum',
    enum: GroupLogAction,
    nullable: true,
  })
  action: GroupLogAction;

  @Column({ name: 'changed_field', nullable: true })
  changedField: string;

  @Column({ name: 'old_value', type: 'text', nullable: true })
  oldValue: string;

  @Column({ name: 'new_value', type: 'text', nullable: true })
  newValue: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Group, (group) => group.changeLogs, { nullable: true })
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @ManyToOne(() => OrgMember, (member) => member.groupChangeLogs, {
    nullable: true,
  })
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
