import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, QueryRunner } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { MessageThreadService } from 'src/modules/messages/services/message-threads.service';
import { SessionStateService } from 'src/modules/security/services/session-state.service';
import { PairChatService } from './pair-chat.service';
import { GroupMembersService } from 'src/modules/groups/services/group-member.service';
import { SessionStateEntity } from 'src/modules/security/entities/session-state.entity';
import { PairChat } from '../entities/pair-chats.entity';

/**
 * Session Management Service
 *
 * Manages cryptographic sessions between users.
 * Handles session establishment, maintenance, and cleanup
 * for secure peer-to-peer communication.
 */
@Injectable()
export class SessionManagementService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly messageThreadService: MessageThreadService,
    private readonly sessionStateService: SessionStateService,
    private readonly pairchatService: PairChatService,
    private readonly groupMemberService: GroupMembersService,
  ) {}

  // NEW: Sync sessions after group membership changes
  async syncSessionsAfterGroupChange(
    groupId: number,
    membersToAdd: number[],
    membersToRemove: number[],
    remainingMembers: number[],
    queryRunner?: QueryRunner,
  ): Promise<void> {
    const manager = queryRunner?.manager || this.dataSource.manager;

    // Step 1: Handle sessions for members that left
    for (const leftMemberId of membersToRemove) {
      // Check sessions with remaining members + new members (the final active state)
      const finalActiveMemberIds = [...remainingMembers, ...membersToAdd];

      for (const otherMemberId of finalActiveMemberIds) {
        await this.checkAndUpdateSessionStateAfterRemoval(
          leftMemberId,
          otherMemberId,
          groupId,
          manager,
        );
      }
    }

    // Step 2: Handle sessions for new members joining
    for (const newMemberId of membersToAdd) {
      // Create/update sessions with existing members
      for (const existingMemberId of remainingMembers) {
        await this.ensureSessionForGroupMembers(
          newMemberId,
          existingMemberId,
          groupId,
          manager,
        );
      }

      // Create sessions between new members themselves
      for (const otherNewMemberId of membersToAdd) {
        if (newMemberId !== otherNewMemberId) {
          await this.ensureSessionForGroupMembers(
            newMemberId,
            otherNewMemberId,
            groupId,
            manager,
          );
        }
      }
    }

    // Step 3: Update group context for remaining members
    for (let i = 0; i < remainingMembers.length; i++) {
      for (let j = i + 1; j < remainingMembers.length; j++) {
        await this.updateSessionGroupContext(
          remainingMembers[i],
          remainingMembers[j],
          groupId,
          manager,
        );
      }
    }
  }

  /**
   * Special method for checking sessions after a member is removed from a group
   */
  private async checkAndUpdateSessionStateAfterRemoval(
    leftMemberId: number,
    remainingMemberId: number,
    removedFromGroupId: number,
    manager: EntityManager,
  ): Promise<void> {
    // Get PairChat
    const pairChatId = await this.pairchatService.findPairChatId(
      leftMemberId,
      remainingMemberId,
      manager,
    );

    if (!pairChatId) return;
    const session = await this.sessionStateService.findActiveByPairChat(
      manager,
      pairChatId,
    );

    if (!session) return;

    // Check shared groups EXCLUDING the group they just left
    const sharedGroups =
      await this.groupMemberService.getSharedGroupIdsExcluding(
        leftMemberId,
        remainingMemberId,
        [removedFromGroupId], // Exclude the group they just left
      );

    const hasSharedGroups = sharedGroups.length > 0;

    if (hasSharedGroups) {
      // Update group context for active session
      session.groupContextIds = sharedGroups;
      await manager.save(SessionStateEntity, session);
    } else {
      // Deactivate session - no shared groups
      console.log(
        `Deactivating session between ${leftMemberId} and ${remainingMemberId}`,
      );
      await this.deactivateSession(session.id, manager);
    }
  }

  private async ensureSessionForGroupMembers(
    member1Id: number,
    member2Id: number,
    groupId: number,
    manager: EntityManager,
  ): Promise<void> {
    // Find or create PairChat
    const pairChatId = await this.pairchatService.findOrCreatePairChatId(
      member1Id,
      member2Id,
      manager,
    );

    // Check if session exists
    let session = await this.sessionStateService.findActiveByPairChat(
      manager,
      pairChatId,
    );

    if (!session) {
      // Check for inactive session
      session = await manager.findOne(SessionStateEntity, {
        where: { pairChatId, isActive: false },
        order: { deactivatedAt: 'DESC' },
      });

      if (session) {
        // Reactivate existing session
        await this.reactivateSession(session.id, [groupId], manager);

        // 🔥 CRITICAL: Link threads with reactivated session
        await this.messageThreadService.linkThreadsWithSession(
          member1Id,
          member2Id,
          session.id,
          true,
          manager,
        );
      } else {
        // Create new session
        session = await this.createNewSession(pairChatId, groupId, manager);

        // 🔥 CRITICAL: Link threads with new session
        await this.messageThreadService.linkThreadsWithSession(
          member1Id,
          member2Id,
          session.id,
          true,
          manager,
        );
      }
    } else {
      // Update group context for active session
      await this.updateSessionGroupContext(
        member1Id,
        member2Id,
        groupId,
        manager,
      );
    }
  }

  private async checkAndUpdateSessionState(
    member1Id: number,
    member2Id: number,
    manager: EntityManager,
  ): Promise<void> {
    // Get PairChat
    const pairChatId = await this.pairchatService.findPairChatId(
      member1Id,
      member2Id,
      manager,
    );

    if (!pairChatId) return;

    // Check if they still share any groups
    const sharedGroups = await this.groupMemberService.getSharedGroupIds(
      member1Id,
      member2Id,
    );
    const hasSharedGroups = sharedGroups.length > 0;

    // Get current session
    const session = await this.sessionStateService.findActiveByPairChat(
      manager,
      pairChatId,
    );

    if (!session) return;

    if (hasSharedGroups) {
      // Update group context for active session
      if (session.isActive) {
        session.groupContextIds = sharedGroups;
        await manager.save(SessionStateEntity, session);
      } else {
        // Reactivate if needed (shouldn't happen but safety check)
        await this.reactivateSession(session.id, sharedGroups, manager);
      }
    } else {
      // Deactivate session - no shared groups
      await this.deactivateSession(session.id, manager);
    }
  }

  /**
   * Create a new session between two members
   */
  private async createNewSession(
    pairChatId: number,
    groupId: number,
    manager: EntityManager,
  ): Promise<SessionStateEntity> {
    const session = manager.create(SessionStateEntity, {
      pairChatId,
      isActive: true,
      lastActiveAt: new Date(),
      groupContextIds: [groupId],
      wasReactivated: false,
    });

    return await manager.save(SessionStateEntity, session);
  }

  /**
   * Reactivate all inactive sessions for a member and enable private messaging
   * Only reactivates sessions if users still share groups
   */
  async reactivateMemberSessions(
    memberId: number,
    manager: EntityManager,
  ): Promise<void> {
    // Get all inactive sessions for this member
    const inactiveSessions = await manager
      .createQueryBuilder(SessionStateEntity, 's')
      .innerJoinAndSelect('s.pairChat', 'pc')
      .where('(pc.member1Id = :memberId OR pc.member2Id = :memberId)', {
        memberId,
      })
      .andWhere('s.isActive = false')
      .getMany();

    for (const session of inactiveSessions) {
      const otherMemberId =
        session.pairChat.member1Id === memberId
          ? session.pairChat.member2Id
          : session.pairChat.member1Id;

      // Check if users still share groups
      const sharedGroups = await this.groupMemberService.getSharedGroupIds(
        memberId,
        otherMemberId,
      );

      if (sharedGroups.length > 0) {
        // Reactivate the session
        await manager.update(SessionStateEntity, session.id, {
          isActive: true,
          wasReactivated: true,
          lastActiveAt: new Date(),
          groupContextIds: sharedGroups,
          deactivatedAt: null,
          updatedAt: new Date(),
        });

        // Enable messaging for private threads between these members
        await this.messageThreadService.updatePrivateThreadMessagingPermission(
          memberId,
          otherMemberId,
          true,
          manager,
        );
      }
    }

    // For already active sessions, just update private thread permissions
    const activeSessions = await manager
      .createQueryBuilder(SessionStateEntity, 's')
      .innerJoinAndSelect('s.pairChat', 'pc')
      .where('(pc.member1Id = :memberId OR pc.member2Id = :memberId)', {
        memberId,
      })
      .andWhere('s.isActive = true')
      .getMany();

    for (const session of activeSessions) {
      const otherMemberId =
        session.pairChat.member1Id === memberId
          ? session.pairChat.member2Id
          : session.pairChat.member1Id;

      // Enable messaging for private threads
      await this.messageThreadService.updatePrivateThreadMessagingPermission(
        memberId,
        otherMemberId,
        true,
        manager,
      );
    }
  }

  /**
   * Deactivate all sessions for a member and disable private messaging
   */
  async deactivateMemberSessions(
    memberId: number,
    manager: EntityManager,
  ): Promise<void> {
    // Get all active sessions for this member
    const activeSessions = await manager
      .createQueryBuilder(SessionStateEntity, 's')
      .innerJoinAndSelect('s.pairChat', 'pc')
      .where('(pc.member1Id = :memberId OR pc.member2Id = :memberId)', {
        memberId,
      })
      .andWhere('s.isActive = true')
      .getMany();

    for (const session of activeSessions) {
      const otherMemberId =
        session.pairChat.member1Id === memberId
          ? session.pairChat.member2Id
          : session.pairChat.member1Id;

      // Deactivate the session
      await manager.update(SessionStateEntity, session.id, {
        isActive: false,
        deactivatedAt: new Date(),
        wasReactivated: false,
        updatedAt: new Date(),
      });

      // Disable messaging for private threads between these members
      await this.messageThreadService.updatePrivateThreadMessagingPermission(
        memberId,
        otherMemberId,
        false,
        manager,
      );
    }
  }

  // NEW: Reactivate a session
  async reactivateSession(
    sessionId: string,
    groupContextIds: number[],
    manager: EntityManager,
  ): Promise<void> {
    await this.sessionStateService.updateSession(manager, sessionId, {
      isActive: true,
      wasReactivated: true,
      lastActiveAt: new Date(),
      groupContextIds,
      deactivatedAt: null,
    });

    await this.messageThreadService.updateThreadsBySession(manager, sessionId, {
      canSendMessages: true,
    });
  }

  async deactivateSession(
    sessionId: string,
    manager: EntityManager,
  ): Promise<void> {
    await this.sessionStateService.updateSession(manager, sessionId, {
      isActive: false,
      deactivatedAt: new Date(),
      wasReactivated: false,
    });

    await this.messageThreadService.updateThreadsBySession(manager, sessionId, {
      canSendMessages: false,
    });
  }

  // NEW: Update session group context
  private async updateSessionGroupContext(
    member1Id: number,
    member2Id: number,
    groupId: number,
    manager: EntityManager,
  ): Promise<void> {
    const pairChatId = await this.pairchatService.findPairChatId(
      member1Id,
      member2Id,
      manager,
    );
    if (!pairChatId) return;

    const session = await this.sessionStateService.findActiveByPairChat(
      manager,
      pairChatId,
    );

    if (session) {
      const currentGroups = session.groupContextIds || [];
      if (!currentGroups.includes(groupId)) {
        session.groupContextIds = [...currentGroups, groupId];
        await manager.save(SessionStateEntity, session);
      }
    }
  }

  async getActiveSessionsForMember(memberId: number): Promise<any[]> {
    return await this.sessionStateService.getActiveSessionsForMember(memberId);
  }

  /**
   * Clean up sessions for deleted/deactivated members
   */
  async cleanupSessionsForMember(memberId: number): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get all pair chats involving this member
      const pairChats = await queryRunner.manager.find(PairChat, {
        where: [{ member1Id: memberId }, { member2Id: memberId }],
      });

      // Deactivate all sessions for these pair chats
      for (const pairChat of pairChats) {
        const session = await this.sessionStateService.findActiveByPairChat(
          queryRunner.manager,
          pairChat.id,
        );

        if (session) {
          await this.deactivateSession(session.id, queryRunner.manager);
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk session management for efficiency
   */
  async bulkUpdateSessions(
    sessionUpdates: Array<{
      sessionId: string;
      updates: Partial<SessionStateEntity>;
    }>,
    manager: EntityManager,
  ): Promise<void> {
    for (const { sessionId, updates } of sessionUpdates) {
      await this.sessionStateService.updateSession(manager, sessionId, updates);
    }
  }
}
