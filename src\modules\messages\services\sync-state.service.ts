import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueryRunner, Repository } from 'typeorm';
import { SyncState } from '../entities/sync-state.entity';
import { MessageThread } from '../entities/message-thread.entity';
import { UpdateSyncStateDto } from '../dto/sync-state.dto';

@Injectable()
export class SyncStateService {
  constructor(
    @InjectRepository(SyncState)
    private readonly syncStateRepo: Repository<SyncState>,
    @InjectRepository(MessageThread)
    private readonly messageThreadRepo: Repository<MessageThread>,
  ) {}

  async findByThreadId(threadId: number): Promise<SyncState> {
    const syncState = await this.syncStateRepo.findOne({
      where: { thread: { id: threadId } },
      relations: ['thread'],
    });

    if (!syncState) {
      throw new NotFoundException(
        `Sync state for thread ${threadId} not found`,
      );
    }

    return syncState;
  }

  async createSyncState(threadId: number): Promise<SyncState> {
    const thread = await this.messageThreadRepo.findOne({
      where: { id: threadId },
    });

    if (!thread) {
      throw new NotFoundException(`Thread ${threadId} not found`);
    }

    const syncState = this.syncStateRepo.create({
      lastSyncedSeq: 0,
      lastAckedSeq: 0,
      hasPending: false,
      threadId: thread.id,
    });

    return await this.syncStateRepo.save(syncState);
  }

  async createSyncStateWithTransaction(
    threadId: number,
    queryRunner: QueryRunner,
  ): Promise<void> {
    const thread = await queryRunner.manager.findOne(MessageThread, {
      where: { id: threadId },
    });
    if (!thread) {
      throw new NotFoundException(`Thread ${threadId} not found`);
    }
    const syncState = queryRunner.manager.create(SyncState, {
      lastSyncedSeq: 0,
      lastAckedSeq: 0,
      hasPending: false,
      threadId: thread.id,
    });

    await queryRunner.manager.save(SyncState, syncState);
  }

  async updateSyncState(
    threadId: number,
    updateDto: UpdateSyncStateDto,
  ): Promise<SyncState> {
    const syncState = await this.findByThreadId(threadId);

    if (updateDto.lastSyncedSeq !== undefined) {
      syncState.lastSyncedSeq = updateDto.lastSyncedSeq;
    }

    if (updateDto.lastAckedSeq !== undefined) {
      syncState.lastAckedSeq = updateDto.lastAckedSeq;
    }

    if (updateDto.hasPending !== undefined) {
      syncState.hasPending = updateDto.hasPending;
    }

    return await this.syncStateRepo.save(syncState);
  }

  async updateLastSyncedSeq(threadId: number, seq: number): Promise<void> {
    await this.syncStateRepo
      .createQueryBuilder()
      .update(SyncState)
      .set({
        lastSyncedSeq: seq,
        updatedAt: new Date(),
      })
      .where('thread_id = :threadId', { threadId })
      .execute();
  }

  async updateLastAckedSeq(threadId: number, seq: number): Promise<void> {
    await this.syncStateRepo
      .createQueryBuilder()
      .update(SyncState)
      .set({
        lastAckedSeq: seq,
        hasPending: false,
        updatedAt: new Date(),
      })
      .where('thread_id = :threadId', { threadId })
      .execute();
  }

  async markAsPending(threadId: number): Promise<void> {
    await this.syncStateRepo
      .createQueryBuilder()
      .update(SyncState)
      .set({
        hasPending: true,
        updatedAt: new Date(),
      })
      .where('thread_id = :threadId', { threadId })
      .execute();
  }

  async clearPending(threadId: number): Promise<void> {
    await this.syncStateRepo
      .createQueryBuilder()
      .update(SyncState)
      .set({
        hasPending: false,
        updatedAt: new Date(),
      })
      .where('thread_id = :threadId', { threadId })
      .execute();
  }

  async getThreadsWithPendingSync(userId: number): Promise<SyncState[]> {
    return await this.syncStateRepo
      .createQueryBuilder('sync')
      .leftJoinAndSelect('sync.thread', 'thread')
      .where('sync.hasPending = true')
      .andWhere('thread.user_id = :userId', { userId })
      .getMany();
  }

  async getSyncStateForUser(
    userId: number,
    chatType: 'group' | 'private',
    targetId: number,
  ): Promise<SyncState | null> {
    const syncState = await this.syncStateRepo
      .createQueryBuilder('sync')
      .leftJoinAndSelect('sync.thread', 'thread')
      .where('thread.user_id = :userId', { userId })
      .andWhere('thread.chat_type = :chatType', { chatType })
      .andWhere('thread.target_id = :targetId', { targetId })
      .getOne();

    return syncState;
  }

  async bulkUpdateSyncStates(
    userIds: number[],
    chatType: 'group' | 'private',
    targetId: number,
    newSeq: number,
    senderId: number,
  ): Promise<void> {
    // Update sync states for all users except sender
    await this.syncStateRepo
      .createQueryBuilder('sync')
      .leftJoin('sync.thread', 'thread')
      .update(SyncState)
      .set({
        lastSyncedSeq: newSeq,
        hasPending: () =>
          `CASE WHEN thread.user_id = ${senderId} THEN false ELSE true END`,
        updatedAt: new Date(),
      })
      .where('thread.user_id IN (:...userIds)', { userIds })
      .andWhere('thread.chat_type = :chatType', { chatType })
      .andWhere('thread.target_id = :targetId', { targetId })
      .execute();
  }

  async getUnsyncedMessageCount(
    userId: number,
    chatType: 'group' | 'private',
    targetId: number,
  ): Promise<number> {
    const syncState = await this.getSyncStateForUser(
      userId,
      chatType,
      targetId,
    );

    if (!syncState) {
      return 0;
    }

    // This would need to be implemented based on your message counting logic
    // For now, returning whether there are pending messages
    return syncState.hasPending ? 1 : 0;
  }

  async resetSyncState(threadId: number): Promise<void> {
    await this.syncStateRepo
      .createQueryBuilder()
      .update(SyncState)
      .set({
        lastSyncedSeq: 0,
        lastAckedSeq: 0,
        hasPending: false,
        updatedAt: new Date(),
      })
      .where('thread_id = :threadId', { threadId })
      .execute();
  }

  async deleteSyncState(threadId: number): Promise<void> {
    await this.syncStateRepo.delete({ thread: { id: threadId } });
  }

  async getSyncStatistics(userId: number): Promise<{
    totalThreads: number;
    threadsWithPending: number;
    lastSyncTime: Date | null;
  }> {
    const [totalThreads, threadsWithPending, lastSyncResult] =
      await Promise.all([
        this.syncStateRepo
          .createQueryBuilder('sync')
          .leftJoin('sync.thread', 'thread')
          .where('thread.user_id = :userId', { userId })
          .getCount(),

        this.syncStateRepo
          .createQueryBuilder('sync')
          .leftJoin('sync.thread', 'thread')
          .where('thread.user_id = :userId', { userId })
          .andWhere('sync.hasPending = true')
          .getCount(),

        this.syncStateRepo
          .createQueryBuilder('sync')
          .leftJoin('sync.thread', 'thread')
          .where('thread.user_id = :userId', { userId })
          .orderBy('sync.updatedAt', 'DESC')
          .limit(1)
          .getOne(),
      ]);

    return {
      totalThreads,
      threadsWithPending,
      lastSyncTime: lastSyncResult?.updatedAt || null,
    };
  }
}
