import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { Group } from './group.entity';

@Entity('group_members')
export class GroupMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'joined_at' })
  joinedAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'left_at', nullable: true })
  leftAt: Date;

  @ManyToOne(() => Group, (group) => group.members)
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.groupMemberships)
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
