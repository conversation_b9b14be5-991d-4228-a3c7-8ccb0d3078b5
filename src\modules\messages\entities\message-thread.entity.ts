import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { SyncState } from './sync-state.entity';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { SessionStateEntity } from 'src/modules/security/entities/session-state.entity';

@Entity('message_threads')
@Index('idx_message_threads_member_chat', ['memberId', 'chatType'])
@Index('idx_message_threads_lookup', ['memberId', 'chatType', 'targetId'])
@Index('idx_message_threads_session', ['sessionId'])
@Index('idx_message_threads_target', ['chatType', 'targetId'])
export class MessageThread {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'chat_type' })
  chatType: ChatType;

  @Column({ name: 'target_id' })
  targetId: number;

  @Column({ name: 'session_id', nullable: true })
  sessionId: string;

  @Column({ name: 'last_message_id', nullable: true })
  lastMessageId: number;

  @Column({ type: 'timestamp', nullable: true })
  lastMessageAt: Date | null;

  @Column({ name: 'unread_count', default: 0 })
  unreadCount: number;

  @Column({ name: 'is_muted', default: false })
  isMuted: boolean;

  @Column({ name: 'is_pinned', default: false })
  isPinned: boolean;

  @Column({ name: 'welcome_message', nullable: true })
  welcomeMessage: string;

  @Column({ name: 'member_count', type: 'int', nullable: true })
  memberCount: number | null;

  @Column({ name: 'can_send_messages', default: true })
  canSendMessages: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  // 🔹 Link with sync state
  @OneToOne(() => SyncState, (sync) => sync.thread, { cascade: true })
  syncState: SyncState;

  @ManyToOne(() => SessionStateEntity, (session) => session.threads, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'session_id' })
  session: SessionStateEntity;
}
