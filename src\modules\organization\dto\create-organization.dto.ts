import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  IsPhoneNumber,
  IsString,
} from 'class-validator';
import { OrganizationStatus } from '../entities/organization.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateOrganizationDto {
  @ApiProperty({
    description: 'Name of the organization',
    example: 'OpenAI Labs',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Location of the organization',
    example: 'San Francisco, CA',
  })
  @IsNotEmpty()
  @IsString()
  location: string;

  @ApiProperty({
    description: 'Contact phone number of the organization',
    example: '+14155552671',
  })
  @IsNotEmpty()
  @IsPhoneNumber()
  phoneNo: string;

  @ApiPropertyOptional({
    description: 'Status of the organization',
    enum: OrganizationStatus,
    example: OrganizationStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(OrganizationStatus)
  status?: OrganizationStatus;

  @ApiPropertyOptional({
    description: 'File URL for organization logo or related document',
    example: 'https://example.com/logo.png',
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}
