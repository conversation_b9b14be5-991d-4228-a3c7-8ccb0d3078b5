import { MemberFcmToken } from '../../../modules/members/entities/member-fcm-token.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  OneToOne,
  Index,
} from 'typeorm';
import { GroupEncryptionKey } from '../../groups/entities/group-encryption-keys.entity';
import { PairChat } from '../../chat/p2p/entities/pair-chats.entity';
import { IdentityKeyEntity } from '../../security/entities/identity-key.entity';
import { SignedPreKeyEntity } from '../../security/entities/signed-prekey.entity';
import { OneTimePreKeyEntity } from '../../security/entities/one-time-prekey.entity';
import { ChainKeyStateEntity } from '../../security/entities/chain-key-states.entity';
import { SessionStateEntity } from '../../security/entities/session-state.entity';
import { MediaEncryptionKey } from '../../media/entities/media-encryption-key.entity';
import { User } from '../../users/entities/user.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { GroupMember } from 'src/modules/groups/entities/group-member.entity';
import { OtpVerification } from 'src/modules/auth/entities';
import { GroupMessage } from 'src/modules/messages/entities/group-message.entity';
import { GroupMessageRead } from 'src/modules/messages/entities/message-read-receipt.entity';
import { PrivateMessage } from 'src/modules/messages/entities/private-message.entity';
import { MemberGlobalSync } from './member-global-sync.entity';
import { MemberEventLog } from '../../audit/entities/member-event-logs.entity';
import { GroupChangeLog } from 'src/modules/audit/entities/group-change-logs.entity';

@Entity('org_members')
@Index(['orgId', 'name'], { unique: true })
export class OrgMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'org_id' })
  orgId: number;

  @Column()
  name: string;

  @Column({ name: 'phone_no', unique: true })
  phoneNo: string;

  @Column({ unique: true })
  email: string;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'is_verified', default: false })
  isVerified: boolean;

  @Column({ name: 'has_signed_prekey', default: false })
  hasSignedPreKey: boolean;

  @Column({ name: 'has_onetime_prekey', default: false })
  hasOneTimePreKey: boolean;

  @Column({ name: 'crypto_keys_updated_at', nullable: true, type: 'timestamp' })
  cryptoKeysUpdatedAt: Date;

  @Column({ name: 'last_login_at', nullable: true, type: 'timestamp' })
  lastLoginAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @Column({ name: 'created_by' })
  createdBy: number;

  @ManyToOne(() => Organization, (org) => org.members)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @OneToMany(() => GroupMember, (member) => member.member)
  groupMemberships: GroupMember[];

  @OneToMany(() => OtpVerification, (otpVerification) => otpVerification.member)
  otpVerifications: OtpVerification[];

  @OneToMany(() => GroupMessage, (message) => message.sender)
  sentMessages: GroupMessage[];

  @OneToMany(() => PrivateMessage, (message) => message.receiver)
  receivedMessages: PrivateMessage[];

  @OneToMany(() => PrivateMessage, (message) => message.sender)
  sentPrivateMessages: PrivateMessage[];

  @OneToMany(() => GroupMessageRead, (read) => read.reader)
  readMessages: GroupMessageRead[];

  @ManyToOne(() => User, (user) => user.createdUsers)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => MemberFcmToken, (fcmToken) => fcmToken.member)
  fcmTokens: MemberFcmToken[];

  @OneToMany(() => GroupEncryptionKey, (encryptionKey) => encryptionKey.member)
  groupEncryptionKeys: GroupEncryptionKey[];

  @OneToMany(() => PairChat, (pairChat) => pairChat.member1)
  pairChatsAsMember1: PairChat[];

  @OneToMany(() => PairChat, (pairChat) => pairChat.member2)
  pairChatsAsMember2: PairChat[];

  // Optional: get all sessions via PairChats
  @OneToMany(() => SessionStateEntity, (session) => session.pairChat)
  sessions: SessionStateEntity[];

  @OneToOne(() => IdentityKeyEntity, (identityKey) => identityKey.member)
  identityKey: IdentityKeyEntity;

  @OneToMany(() => SignedPreKeyEntity, (signedPreKey) => signedPreKey.member)
  signedPreKeys: SignedPreKeyEntity[];

  @OneToMany(() => OneTimePreKeyEntity, (oneTimePreKey) => oneTimePreKey.member)
  oneTimePreKeys: OneTimePreKeyEntity[];

  @OneToMany(() => ChainKeyStateEntity, (chainKeyState) => chainKeyState.owner)
  chainKeyStates: ChainKeyStateEntity[];

  @OneToMany(() => MediaEncryptionKey, (key) => key.recipient)
  mediaKeys: MediaEncryptionKey[];

  @OneToOne(() => MemberGlobalSync, (sync) => sync.member)
  globalSync: MemberGlobalSync;

  // 🔗 One-to-many with MemberEventLog
  @OneToMany(() => MemberEventLog, (eventLog) => eventLog.member)
  eventLogs: MemberEventLog[];

  // 🔗 One-to-many with GroupChangeLog
  @OneToMany(() => GroupChangeLog, (changeLog) => changeLog.member)
  groupChangeLogs: GroupChangeLog[];
}
