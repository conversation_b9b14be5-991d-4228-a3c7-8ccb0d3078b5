import {
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Res,
  Body,
  Get,
  Param,
  ParseIntPipe,
  UseInterceptors,
  Query,
  Req,
  UnauthorizedException,
  Put,
  NotFoundException,
  Delete,
  Logger,
} from '@nestjs/common';
import { CreateGroupDto } from '../dto/create-group.dto';
import { Request, Response } from 'express';
import { GroupsService } from '../services/groups.service';
import { Group } from '../entities/group.entity';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudStorageInterceptor } from '../../../common/interceptors/cloud-storage.interceptor';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UpdateGroupDto } from '../dto/update-group.dto';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { GroupMember } from '../entities/group-member.entity';

interface GroupWithIsActive extends Group {
  isActive: boolean;
}

@ApiTags('Groups')
@ApiBearerAuth()
@Controller('groups')
export class GroupsController {
  private readonly logger = new Logger(GroupsController.name);
  constructor(private readonly groupsService: GroupsService) {}

  @Post('create/:userId')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a new group' })
  @ApiParam({
    name: 'userId',
    type: Number,
    description: 'ID of the user creating the group',
  })
  @ApiBody({ type: CreateGroupDto })
  @ApiResponse({
    status: 200,
    description: 'Group created successfully',
    type: Group,
  })
  async create(
    @Param('userId') userId: string,
    @Body() createGroupDto: CreateGroupDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Group> {
    const user = request.user;
    if (Number(user.id) !== Number(userId)) throw new UnauthorizedException();
    const group = await this.groupsService.create(
      createGroupDto,
      Number(userId),
    );
    response.locals.message = 'Group created successfully';
    return group;
  }

  // @Post('allocate-members/:userId')
  // @HttpCode(HttpStatus.OK)
  // async allocateMembersToGroup(
  //   @Param('userId') userId: string,
  //   @Body() allocateMemberDto: AllocateMemberDto,
  //   @Res({ passthrough: true }) response: Response,
  //   @Req() request: Request,
  // ): Promise<GroupMember[]> {
  //   const user = request.user;

  //   if (Number(user.id) !== Number(userId)) {
  //     throw new UnauthorizedException();
  //   }

  //   const allocatedMembers = await this.groupsService.allocateGroupMembers(
  //     allocateMemberDto,
  //     userId,
  //   );

  //   response.locals.message = 'Members allocated to group successfully';

  //   return allocatedMembers;
  // }

  @Get('organization')
  @UseInterceptors(ImageUrlInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all groups by organization ID' })
  @ApiQuery({ name: 'orgId', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Groups fetched successfully',
    type: [Group],
  })
  async getGroupsByOrgId(
    @Query('orgId', ParseIntPipe) orgId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<GroupWithIsActive[]> {
    const user = request.user;
    if (user.orgId !== Number(orgId)) throw new UnauthorizedException();
    const groups = await this.groupsService.findAllByOrgId(orgId);
    return groups.map((group) => ({
      ...group,
      isActive: group.deletedAt === null,
    }));
  }

  @Get('group-members')
  @UseInterceptors(ImageUrlInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all groups with members for an organization' })
  @ApiQuery({ name: 'orgId', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Group members fetched successfully',
    type: [GroupMember],
  })
  async getAllGroupsWithMembers(
    @Query('orgId', ParseIntPipe) orgId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ) {
    const user = request.user;
    if (user.orgId !== Number(orgId)) throw new UnauthorizedException();
    const result = await this.groupsService.getAllGroupsWithMembers(orgId);
    response.locals.message = 'Group members fetched successfully';
    return result;
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @ApiOperation({ summary: 'Update a group' })
  @ApiParam({ name: 'id', type: Number, description: 'Group ID' })
  @ApiBody({ type: UpdateGroupDto })
  @ApiResponse({
    status: 200,
    description: 'Group updated successfully',
    type: Group,
  })
  async updateGroup(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateGroupDto: UpdateGroupDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Group> {
    const user = request.user;
    const group = await this.groupsService.findOne(id);
    if (!group) throw new NotFoundException('Group not found');
    if (group.createdBy !== user.id) throw new UnauthorizedException();
    const result = await this.groupsService.updateGroup(
      id,
      updateGroupDto,
      user.id,
    );
    response.locals.message = 'Group updated successfully';
    return result;
  }

  // @Put(':userId/update-members')
  // async updateGroupMembers(
  //   @Param('userId') userId: string,
  //   @Req() req: Request,
  //   @Body() updateGroupMemberDto: AllocateMemberDto,
  // ) {
  //   (req as any).res.locals.message = 'Group members updated successfully';

  //   const user = req.user;

  //   if (Number(userId) !== Number(user.id)) {
  //     throw new UnauthorizedException();
  //   }

  //   return await this.groupsService.allocateGroupMembers(
  //     updateGroupMemberDto,
  //     userId,
  //   );
  // }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a group' })
  @ApiParam({ name: 'id', type: Number, description: 'Group ID' })
  @ApiResponse({
    status: 200,
    description: 'Group deleted successfully',
    type: Object,
  })
  async deleteGroup(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<{ message: string }> {
    const user = request.user;
    const group = await this.groupsService.findOne(id);
    if (!group) throw new NotFoundException('Group not found');
    if (group.createdBy !== user.id) throw new UnauthorizedException();
    await this.groupsService.deleteGroup(id, user.id);
    response.locals.message = 'Group deleted successfully';
    return { message: 'Group deleted successfully' };
  }
}
