import { GroupMessage } from 'src/modules/messages/entities/group-message.entity';
import { PrivateMessage } from 'src/modules/messages/entities/private-message.entity';

// Add this utility function
export function getMessagePreview(
  message: GroupMessage | PrivateMessage,
): string {
  if (!message) return '';

  // If it's a text message with content
  if (message.encryptedContent && !message.file) {
    return '';
  }

  // If it's a media messages
  if (message.file) {
    const fileType = message.file.fileType;
    const caption = message.caption;

    switch (fileType) {
      case 'image':
        return caption ? `🖼️ ${caption}` : '🖼️ Photo';
      case 'video':
        return caption ? `🎥 ${caption}` : '🎥 Video';
      case 'audio':
        return caption ? `🎵 ${caption}` : '🎵 Audio';
      case 'document':
        const fileName = message.file.filename || 'Document';
        return caption ? `📄 ${caption}` : `📄 ${fileName}`;
      default:
        return caption || 'Media';
    }
  }

  return '';
}
