import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { Role } from './role.entity';
import { Organization } from '../../organization/entities/organization.entity';
import { Group } from '../../groups/entities/group.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('users')
@Index(['username'], { unique: true })
@Index(['email'], { unique: true })
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  username: string;

  @Column()
  password: string;

  @Column({ unique: true })
  email: string;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'role_id' })
  roleId: number;

  @ManyToOne(() => Role, (role) => role.users)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  // Only for organization admins
  @Column({ name: 'org_id', nullable: true })
  orgId: number;

  @Column({ nullable: true })
  encryptedAdminSecretKey?: string;

  @Column({ nullable: true })
  publicKey?: string;

  @Column({ nullable: true })
  adminSecretKeyNonce?: string;

  @Column({ nullable: true })
  adminSecretKeySalt?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToOne(() => Organization, (organization) => organization.admins, {
    nullable: true,
  })
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @OneToMany(() => Group, (group) => group.creator)
  createdGroups: Group[];

  @OneToMany(() => OrgMember, (member) => member.creator)
  createdUsers: OrgMember[];

  @OneToMany(() => Group, (group) => group.deleter)
  deletedGroups: Group[];
}
