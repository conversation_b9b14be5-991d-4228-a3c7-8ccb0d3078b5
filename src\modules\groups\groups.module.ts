import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Group } from './entities/group.entity';
import { GroupMember } from './entities/group-member.entity';
import { GroupsService } from './services/groups.service';
import { GroupsController } from './controllers/groups.controller';
import { GroupEncryptionService } from './services/group-encryption.service';
import { GroupEncryptionKey } from './entities/group-encryption-keys.entity';
import { GroupMembersService } from './services/group-member.service';
import { GroupMembersController } from './controllers/group-member.controller';
import { MembersModule } from '../members/members.module';
import { SocketModule } from '../../infrastructure/socket/socket.module';
import { UserModule } from '../users/users.module';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from '../../core/core.module';
import { PresenceNotifyHandler } from './handler/presence-handler';
import { AuditModule } from '../audit/audit.module';
import { NotificationModule } from 'src/infrastructure/notification/notification.module';
import { RedisModule } from 'src/infrastructure/redis/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Group, GroupMember, GroupEncryptionKey]),
    CoreModule,
    MembersModule,
    SocketModule,
    NotificationModule,
    RedisModule,
    UserModule,
    OrganizationsModule,
    AuditModule,
  ],
  providers: [
    GroupsService,
    GroupEncryptionService,
    GroupMembersService,
    PresenceNotifyHandler,
  ],
  controllers: [GroupsController, GroupMembersController],
  exports: [GroupsService, GroupMembersService, GroupEncryptionService],
})
export class GroupsModule {}
