import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Between,
  In,
  MoreThan,
  More<PERSON>han<PERSON>r<PERSON>qua<PERSON>,
  Query<PERSON><PERSON><PERSON>,
  Repository,
} from 'typeorm';
import { SendGroupMessageDto } from '../dto/send-group-message.dto';
import { GroupMessage } from '../entities/group-message.entity';
import { GroupMessageRead } from '../entities/message-read-receipt.entity';
import { MembersService } from '../../members/services/members.service';
import { GroupsService } from '../../groups/services/groups.service';
import { GroupMembersService } from '../../groups/services/group-member.service';
import { MessageSentEvent } from 'src/common/events';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';
import { MessageThreadService } from './message-threads.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { StorageService } from 'src/core/storage/storage.service';
import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';

@Injectable()
export class GroupMessagesService {
  constructor(
    @InjectRepository(GroupMessage)
    private readonly groupmessageRepo: Repository<GroupMessage>,
    @InjectRepository(GroupMessageRead)
    private readonly groupMessageReadRepo: Repository<GroupMessageRead>,

    private readonly membersService: MembersService,
    private readonly groupsService: GroupsService,
    private readonly groupmembersService: GroupMembersService,
    private readonly eventBus: EventBusService,
    private readonly messageThreadService: MessageThreadService,
    private readonly storageService: StorageService,
  ) {}

  // async getNextSequenceNumber(
  //   groupId: number,
  //   queryRunner: QueryRunner,
  // ): Promise<number> {
  //   const result = await queryRunner.manager
  //     .createQueryBuilder(GroupMessage, 'gm')
  //     .select('COALESCE(MAX(gm.seq), 0)', 'maxSeq')
  //     .where('gm.groupId = :groupId', { groupId })
  //     .getRawOne<{ maxSeq: number }>();

  //   return (result?.maxSeq || 0) + 1;
  // }

  async saveMessage(
    sendMessagedto: SendGroupMessageDto,
  ): Promise<GroupMessage> {
    const group = await this.groupsService.findOne(
      Number(sendMessagedto.groupId),
    );
    const sender = await this.membersService.findOne(sendMessagedto.senderId);

    if (!group || !sender) {
      throw new Error('Invalid sender or group');
    }

    const file = sendMessagedto.fileId
      ? await this.groupmembersService.findOneFile(sendMessagedto.fileId)
      : null;

    const maxSeqResult = await this.groupmessageRepo
      .createQueryBuilder('msg')
      .select('MAX(msg.seq)', 'maxSeq')
      .where('msg.groupId = :groupId', { groupId: group.id })
      .getRawOne<{ maxSeq: string }>();

    const nextSeq = parseInt(maxSeqResult?.maxSeq || '0', 10) + 1;

    const messageToSave = this.groupmessageRepo.create({
      group: group,
      sender: sender,
      groupKeyVersion: group.currentKeyVersion,
      // seq: nextSeq,
      encryptedContent: sendMessagedto.encryptedContent,
      encryptedMetaData: sendMessagedto.encryptedMetaData || {},
      nonce: sendMessagedto.nonce,
      replyToMessageId:
        sendMessagedto.replyToMessageId !== undefined
          ? sendMessagedto.replyToMessageId
          : undefined,
      file: file || undefined,
      sentAt: sendMessagedto.sentAt || new Date(),
    });

    const savedMessage = await this.groupmessageRepo.save(messageToSave);

    // Fetch the complete message with relations
    const message = await this.groupmessageRepo.findOne({
      where: { id: savedMessage.id },
      relations: [
        'sender',
        'file',
        'replyTo',
        'replyTo.sender',
        'reads',
        'group',
      ],
    });

    if (!message) {
      throw new NotFoundException('Message not found after saving');
    }

    // Update message threads for all group members
    await this.updateMessageThreads(message);

    return message;
  }

  async saveAndBroadcastMessage(
    sendMessageDto: SendGroupMessageDto,
    excludeSenderId?: boolean,
    retryCount?: number,
  ): Promise<GroupMessage> {
    // Save message first to get sequence number
    const savedMessage = await this.saveMessage(sendMessageDto);

    // Get group members
    const groupMembers = await this.groupsService.getVerifiedGroupMemberIds(
      sendMessageDto.groupId,
    );

    const retryCounts = retryCount ?? 0;

    const targetMembers: number[] = excludeSenderId
      ? groupMembers.filter((id: number) => id !== sendMessageDto.senderId)
      : groupMembers;

    const memberNotificationInfo: MemberNotificationInfo[] = [];

    for (const memberId of targetMembers) {
      const thread = await this.messageThreadService.getThreadByTargetId(
        memberId,
        ChatType.GROUP,
        sendMessageDto.groupId,
      );

      const isMuted = thread?.isMuted || false;

      memberNotificationInfo.push({
        memberId,
        silent: isMuted,
      });
    }

    const signedGroupImageUrl = await this.storageService.generateSignedUrl(
      savedMessage.group.imageUrl,
      24 * 60 * 60,
    );

    // Prepare message data for broadcasting
    const messageData = {
      id: savedMessage.id,
      // seq: savedMessage.seq,
      groupId: savedMessage.groupId,
      senderId: savedMessage.senderId,
      encryptedContent: savedMessage.encryptedContent,
      messageGroupKeyVersion: savedMessage.groupKeyVersion,
      sentAt: savedMessage.sentAt,
      sender: savedMessage.sender,
      replyTo: savedMessage.replyTo,
      file: savedMessage.file,
    };

    const notificationPayload: PushNotification = {
      title: savedMessage.group.name,
      body: `${savedMessage.sender.name} sent a new message`,
      data: messageData,
      imageUrl: signedGroupImageUrl,
    };

    // Publish event for message delivery
    this.eventBus.publish(
      new MessageSentEvent(
        savedMessage.id,
        savedMessage.senderId,
        memberNotificationInfo,
        notificationPayload,
        retryCounts,
      ),
    );

    return savedMessage;
  }

  async incrementDeliveryAttempts(messageId: number): Promise<void> {
    await this.groupmessageRepo.update(messageId, {
      deliveredCount: () => 'delivered_count + 1',
    });
  }

  async markAsDeliveryFailed(messageId: number): Promise<void> {
    await this.groupmessageRepo.update(messageId, {
      deliveredCount: -1, // Mark as permanently failed
    });
  }

  async markMessageAsRead(
    groupId: number,
    readerId: number,
  ): Promise<GroupMessageRead[] | null> {
    const existingMessages = await this.groupmessageRepo.find({
      where: { groupId, isDeleted: false },
      select: ['id'],
    });
    const allMessageIds = existingMessages.map((m) => m.id);

    if (allMessageIds.length === 0) {
      console.log('No messages found to mark as read');
      return null;
    }
    const alreadyRead = await this.groupMessageReadRepo.find({
      where: {
        readerId,
        messageId: In(allMessageIds),
      },
      select: ['messageId'],
    });
    const alreadyReadIds = new Set(alreadyRead.map((r) => r.messageId));
    const unreadMessageIds = allMessageIds.filter(
      (id) => !alreadyReadIds.has(id),
    );

    if (unreadMessageIds.length === 0) {
      console.log('All messages already read');
      return null;
    }

    const newReads = unreadMessageIds.map((messageId) =>
      this.groupMessageReadRepo.create({
        messageId,
        readerId,
        readAt: new Date(),
      }),
    );

    const savedReads = await this.groupMessageReadRepo.save(newReads);

    return savedReads.length > 0 ? savedReads : null;
  }

  async findMessagesByGroup(groupId: number): Promise<GroupMessage[]> {
    return this.groupmessageRepo.find({
      where: { group: { id: groupId } },
      order: { sentAt: 'DESC' },
      relations: ['sender', 'file', 'replyTo'],
    });
  }

  private async updateMessageThreads(message: GroupMessage): Promise<void> {
    try {
      // Get all group members
      const groupMembers = await this.groupsService.getGroupMemberIds(
        message.groupId,
      );

      // Bulk update threads for all group members
      await this.messageThreadService.bulkUpdateThreadsForMessage(
        groupMembers,
        ChatType.GROUP,
        message.groupId,
        message.id,
        // message.seq,
        message.senderId,
      );

      // Alternative approach: Update individual threads (use this if bulk update is not working properly)
      /*
      const updatePromises = groupMembers.map(async (memberId) => {
        const isFromSelf = memberId === message.senderId;
        
        return this.messageThreadService.updateThreadAfterMessage(
          memberId,
          'group',
          message.groupId,
          message.id,
          message.seq,
          isFromSelf,
        );
      });

      await Promise.allSettled(updatePromises);
      */
    } catch (error) {
      // Log error but don't fail the message save
      console.error('Failed to update message threads:', error);
    }
  }

  async countGroupMessagesReadByMember(
    groupId: number,
    memberId: number,
  ): Promise<number> {
    return await this.groupMessageReadRepo.count({
      where: {
        message: { groupId },
        readerId: memberId,
      },
    });
  }
  async findOneMessage(id: number): Promise<GroupMessage | null> {
    return this.groupmessageRepo.findOne({ where: { id } });
  }

  async getTotalMessagesInGroup(
    groupId: number,
    joinedAt?: Date,
  ): Promise<number> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    return this.groupmessageRepo.count({
      where: whereCondition,
    });
  }

  async getLastMessageInGroup(
    groupId: number,
    joinedAt?: Date,
    lastSyncDate?: Date,
  ): Promise<GroupMessage | null> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
      ...(lastSyncDate && { updatedAt: MoreThan(lastSyncDate) }),
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    return this.groupmessageRepo.findOne({
      where: whereCondition,
      order: { sentAt: 'DESC' },
      relations: ['sender'],
    });
  }

  // async deleteMessageForMember(
  //   messageId: number,
  //   memberId: number,
  // ): Promise<void> {
  //   console.log(`Deleting message ${messageId} for member ${memberId}`);

  //   const repo = this.messageVisibilityRepository;

  //   const existing = await repo.findOne({
  //     where: { messageId, memberId },
  //   });

  //   if (existing) {
  //     // Soft delete
  //     await repo.softRemove(existing);
  //   } else {
  //     // Create and soft delete if not already tracked
  //     const newVisibility = repo.create({
  //       messageId,
  //       memberId,
  //       isVisible: false,
  //     });
  //     const savedVisibility = await repo.save(newVisibility);
  //   }
  // }

  async getMessagesInGroup(
    groupId: number,
    joinedAt?: Date,
  ): Promise<GroupMessage[]> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    const messages = await this.groupmessageRepo.find({
      where: whereCondition,
      relations: [
        'sender',
        'file',
        'visibilities',
        'replyTo',
        'replyTo.sender',
        'reads',
      ],
      order: { sentAt: 'DESC' },
    });

    return messages;
  }

  async deleteMessagesForEveryone(
    messageIds: number[],
    memberId: number,
  ): Promise<number[]> {
    const messages = await this.groupmessageRepo.findBy({
      id: In(messageIds),
      isDeleted: false,
    });

    const notFound = messageIds.filter(
      (id) => !messages.find((msg) => msg.id === id),
    );
    if (notFound.length) {
      throw new NotFoundException(`Messages not found: ${notFound.join(', ')}`);
    }

    const unauthorized = messages.filter((msg) => msg.senderId !== memberId);
    if (unauthorized.length) {
      throw new ForbiddenException(
        `Only your own messages can be deleted. Unauthorized: ${unauthorized
          .map((m) => m.id)
          .join(', ')}`,
      );
    }

    for (const msg of messages) {
      msg.isDeleted = true;
    }

    await this.groupmessageRepo.save(messages);

    return messages.map((msg) => msg.id); // return deleted IDs
  }

  async findLastMessageAfterJoin(
    groupId: number,
    joinedAt: Date,
  ): Promise<GroupMessage | null> {
    return this.groupmessageRepo.findOne({
      where: {
        groupId,
        sentAt: MoreThanOrEqual(joinedAt),
        isDeleted: false,
      },
      order: { sentAt: 'DESC' },
      relations: ['sender', 'file'],
    });
  }

  // async hideMessageForMember(
  //   messageId: number,
  //   memberId: number,
  // ): Promise<void> {
  //   const existing = await this.messageVisibilityRepository.findOne({
  //     where: { messageId, memberId },
  //   });

  //   if (existing) {
  //     existing.isVisible = false;
  //     existing.deletedAt = new Date();
  //     await this.messageVisibilityRepository.save(existing);
  //   } else {
  //     await this.messageVisibilityRepository.save({
  //       messageId,
  //       memberId,
  //       isVisible: false,
  //       deletedAt: new Date(),
  //     });
  //   }
  // }

  async getDeletedMessagesSince(groupId: number, lastOffline: Date) {
    return this.groupmessageRepo.find({
      where: {
        groupId,
        isDeleted: true,
      },
      select: ['id'],
    });
  }

  async getMessagesInGroupBetween(
    groupId: number,
    fromRaw: Date | string,
    toRaw: Date | string,
  ) {
    const from = new Date(fromRaw);
    const to = new Date(toRaw);

    const fromUTC = new Date(from.toISOString());
    const toUTC = new Date(to.toISOString());
    return this.groupmessageRepo.find({
      where: {
        groupId,
        sentAt: Between(fromUTC, toUTC),
      },
      relations: ['sender', 'file', 'reads'],
      order: { sentAt: 'DESC' },
    });
  }
}
