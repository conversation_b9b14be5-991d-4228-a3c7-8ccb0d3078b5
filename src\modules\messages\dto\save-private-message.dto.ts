import { IsNumber, IsString, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class SavePrivateMessageDto {
  @IsNumber()
  senderId: number;

  @IsNumber()
  receiverId: number;

  @IsString()
  encryptedContent: string;

  @IsString()
  @IsOptional()
  ephemeralPublicKey?: string;

  @IsNumber()
  @IsOptional()
  messageIndex?: number;

  @IsNumber()
  @IsOptional()
  previousChainLength?: number;

  @IsNumber()
  @IsOptional()
  chainKeyVersion?: number;

  @IsNumber()
  @IsOptional()
  replyToMessageId?: number;

  @IsString()
  @IsOptional()
  caption?: string;

  @IsNumber()
  @IsOptional()
  fileId?: number;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  sentAt?: Date;
}
