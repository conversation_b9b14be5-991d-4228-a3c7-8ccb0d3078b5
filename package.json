{"name": "chat-app", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "cross-env NODE_ENV=production nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=production nest start --debug --watch", "start:prod": "node dist/main", "build": "cross-env NODE_ENV=production nest build", "seed": "ts-node -r tsconfig-paths/register src/database/seed.ts", "typeorm": "typeorm-ts-node-commonjs -d src/database/data-source.ts", "migration:generate": "cross-env NAME=$npm_config_name npm run generate-migration", "generate-migration": "sh -c 'typeorm-ts-node-commonjs -d src/database/data-source.ts migration:generate src/database/migrations/$NAME'", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@golevelup/nestjs-rabbitmq": "^5.3.0", "@google-cloud/storage": "^7.16.0", "@grpc/grpc-js": "^1.13.4", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^10.4.20", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.4", "@nestjs/platform-socket.io": "^10.4.20", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.1", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.17", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/semantic-conventions": "^1.36.0", "@roamhq/wrtc": "^0.9.0", "@willsoto/nestjs-prometheus": "^6.0.2", "argon2": "^0.41.1", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "ed2curve": "^0.3.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "ioredis": "^5.4.1", "multer": "^2.0.2", "nodemailer": "^6.9.15", "opossum": "^9.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.0", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "simple-peer": "^9.11.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "typeorm": "^0.3.20", "typeorm-extension": "^3.7.1", "webpack": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cookie-parser": "^1.4.8", "@types/cron": "^2.0.1", "@types/ed2curve": "^0.2.4", "@types/express": "^4.17.17", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.17.32", "@types/nodemailer": "^6.4.16", "@types/opossum": "^8.1.9", "@types/sharp": "^0.22.1", "@types/socket.io": "^3.0.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}