export enum MemberEventType {
  MESSAGE = 'message',
  GROUP_UPDATE = 'group_update',
  SESSION_CHANGE = 'session_change',
  THREAD_UPDATE = 'thread_update',
  MEMBER_UPDATE = 'member_update',
  KEY_CHANGE = 'key_change',
  MEMBER_VERIFICATION = 'member_verification',
  MEMBER_CHANGE = 'member_change',
  FILE_OPERATION = 'file_operation',
}

export enum MemberChangeAction {
  ADDED_TO_GROUP = 'added_to_group',
  REMOVED_FROM_GROUP = 'removed_from_group',
  PROMOTED_TO_CO_MEMBER = 'promoted_to_co_member',
  DEMOTED_FROM_CO_MEMBER = 'demoted_from_co_member'
}