export enum GroupChangeType {
  META = 'meta', // Group metadata changes
  MEMBER = 'member', // Member-related changes
  KEY = 'key', // Group key changes
}

export enum GroupLogAction {
  CREATE = 'create', // Group creation
  UPDATE = 'update', // General update
  DELETE = 'delete', // Group deletion
  JOIN = 'join', // Member joined
  LEAVE = 'leave', // Member left
  KEY_ROTATE = 'key_rotate', // Group key rotation
  KEY_UPDATE = 'key_update', // Group key update
}
