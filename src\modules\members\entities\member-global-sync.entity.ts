import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrgMember } from './org-member.entity';

@Entity('member_global_sync')
export class MemberGlobalSync {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ name: 'member_id', type: 'int', unique: true })
  memberId: number;

  @Column({ name: 'global_seq', type: 'bigint', default: 0 })
  globalSeq: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @OneToOne(() => OrgMember, (member) => member.globalSync, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
