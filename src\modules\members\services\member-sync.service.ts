import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { MemberEventLog } from '../../audit/entities/member-event-logs.entity';
import { MemberGlobalSync } from '../entities/member-global-sync.entity';
import { MemberEventType } from '../../audit/enums/member-event.enum';
import { EventLogService } from 'src/modules/audit/services/member-event.service';

interface MemberEventInput {
  memberId: number;
  eventType: MemberEventType;
  sourceTable?: string;
  sourceId?: string;
}

@Injectable()
export class MemberSyncService {
  constructor(
    @InjectRepository(MemberGlobalSync)
    private readonly globalSyncRepo: Repository<MemberGlobalSync>,

    private readonly dataSource: DataSource,

    private readonly eventLogService: EventLogService,
  ) {}

  async saveEvent(
    input: MemberEventInput,
    manager?: EntityManager,
  ): Promise<MemberEventLog> {
    const executor = manager ?? this.dataSource.manager;

    // 1. First lock the global sync row (or create if doesn't exist)
    let globalSync = await executor.getRepository(MemberGlobalSync).findOne({
      where: { memberId: input.memberId },
      // lock: { mode: 'pessimistic_write' },
    });

    // 2. Ensure numeric conversion
    const currentGlobalSeq = globalSync
      ? parseInt(globalSync.globalSeq.toString(), 10)
      : 0;
    const nextGlobalSeq = currentGlobalSeq + 1;

    if (!globalSync) {
      globalSync = executor.create(MemberGlobalSync, {
        memberId: input.memberId,
        globalSeq: nextGlobalSeq,
      });
    } else {
      globalSync.globalSeq = nextGlobalSeq;
    }

    await executor.save(globalSync);

    // 3. Create event log
    return this.eventLogService.createEventLog(
      input.memberId,
      executor,
      input.eventType,
      input.sourceTable,
      input.sourceId,
    );
  }
}
