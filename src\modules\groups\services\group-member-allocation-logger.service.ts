import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { GroupChangeLog } from '../../audit/entities/group-change-logs.entity';
import { GroupChangeType, GroupLogAction } from '../../audit/enums/group-log-action.enum';
import { MemberEventLog } from '../../audit/entities/member-event-logs.entity';
import { MemberEventType, MemberChangeAction } from '../../audit/enums/member-event.enum';
import { MemberGlobalSync } from '../../members/entities/member-global-sync.entity';

@Injectable()
export class GroupMemberAllocationLoggerService {
  constructor(private readonly dataSource: DataSource) {}

  async logAllocationChange(params: {
    groupId: number;
    actingMemberId: number;
    removedMembers: number[];
    addedMembers: number[];
    newKeyVersion: number;
    oldKeyVersion: number;
    finalMembers: number[];
    groupSeq: number;
    groupKeyRowId: number;
    groupMemberRowIds: Record<number, number>; // map memberId -> group_member row id
  }) {
    const {
      groupId,
      actingMemberId,
      removedMembers,
      addedMembers,
      newKeyVersion,
      oldKeyVersion,
      finalMembers,
      groupSeq,
      groupKeyRowId,
      groupMemberRowIds,
    } = params;

    await this.dataSource.transaction(async (manager) => {
      // 1) Log group-level change
      await manager.save(GroupChangeLog, {
        memberId: actingMemberId,
        groupId,
        groupSeq,
        changeType: GroupChangeType.MEMBER,
        action: GroupLogAction.UPDATE,
        changedField: 'membership',
        oldValue: JSON.stringify({ removedMembers }),
        newValue: JSON.stringify({ addedMembers }),
      });

      // 2) Log removed members
      for (const memberId of removedMembers) {
        const nextSeq = await this.incrementMemberSeq(manager, memberId);
        await manager.save(MemberEventLog, {
          memberId,
          memberSeq: nextSeq,
          eventType: MemberEventType.MEMBER_CHANGE,
          sourceTable: 'group_members',
          sourceId: groupMemberRowIds[memberId] || null,
          metadata: {
            groupId,
            action: MemberChangeAction.REMOVED_FROM_GROUP,
          },
        });
      }

      // 3) Log added members
      for (const memberId of addedMembers) {
        const nextSeq = await this.incrementMemberSeq(manager, memberId);
        await manager.save(MemberEventLog, {
          memberId,
          memberSeq: nextSeq,
          eventType: MemberEventType.MEMBER_CHANGE,
          sourceTable: 'group_members',
          sourceId: groupMemberRowIds[memberId] || null,
          metadata: {
            groupId,
            action: MemberChangeAction.ADDED_TO_GROUP,
          },
        });
      }

      // 4) Log key change for all final members
      for (const memberId of finalMembers) {
        const nextSeq = await this.incrementMemberSeq(manager, memberId);
        await manager.save(MemberEventLog, {
          memberId,
          memberSeq: nextSeq,
          eventType: MemberEventType.KEY_CHANGE,
          sourceTable: 'group_encryption',
          sourceId: groupKeyRowId,
          metadata: {
            groupId,
            oldVersion: oldKeyVersion,
            newVersion: newKeyVersion,
          },
        });
      }
    });
  }

  private async incrementMemberSeq(manager, memberId: number): Promise<number> {
    const sync = await manager.findOne(MemberGlobalSync, { where: { memberId } });
    if (!sync) {
      const created = await manager.save(MemberGlobalSync, { memberId, globalSeq: 1 });
      return created.globalSeq;
    }
    sync.globalSeq += 1;
    await manager.save(sync);
    return sync.globalSeq;
  }
}
