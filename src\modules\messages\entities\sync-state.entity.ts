import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { MessageThread } from './message-thread.entity';

@Entity('sync_state')
@Index(['threadId'], { unique: true })
export class SyncState {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'thread_id' })
  threadId: number;

  @Column({ type: 'bigint', default: 0 })
  lastSyncedSeq: number;

  @Column({ type: 'bigint', default: 0 })
  lastAckedSeq: number;

  @Column({ default: false })
  hasPending: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @OneToOne(() => MessageThread, (thread) => thread.syncState, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'thread_id' })
  thread: MessageThread;
}
