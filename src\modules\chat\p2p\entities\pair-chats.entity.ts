import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrgMember } from '../../../members/entities/org-member.entity';
import { SessionStateEntity } from 'src/modules/security/entities/session-state.entity';

@Entity('pair_chats')
@Index(['member1Id', 'member2Id'], { unique: true })
export class PairChat {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  member1Id: number;

  @Column()
  member2Id: number;

  @Column({ default: 1 })
  currentKeyVersion: number;

  @Column({ default: true })
  isActive: boolean;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'member1Id' })
  member1: OrgMember;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'member2Id' })
  member2: OrgMember;

  @OneToMany(() => SessionStateEntity, (session) => session.pairChat)
  sessions: SessionStateEntity[];
}
