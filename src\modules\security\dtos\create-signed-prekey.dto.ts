// create-signed-prekey.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsDateString } from 'class-validator';

export class CreateSignedPreKeyDto {
  @ApiProperty({ description: 'Unique keyId for signed prekey' })
  @IsNumber()
  keyId: number;

  @ApiProperty({ description: 'Public key for signed prekey' })
  @IsString()
  publicKey: string;

  @ApiProperty({ description: 'Signature of signed prekey' })
  @IsString()
  signature: string;

  @ApiProperty({ description: 'Expiration date of signed prekey' })
  @IsDateString()
  expireAt: string;
}
