import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { MediaFile } from './media-file.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('media_keys')
@Index(['mediaId', 'recipientId'], { unique: true })
export class MediaEncryptionKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  mediaId: number;

  @Column()
  recipientId: number;

  @Column({ type: 'text' })
  wrappedCek: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @ManyToOne(() => MediaFile, (media) => media.encryptionKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'mediaId' })
  media: MediaFile;

  @ManyToOne(() => OrgMember, (member) => member.mediaKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'recipientId' })
  recipient: OrgMember;
}
