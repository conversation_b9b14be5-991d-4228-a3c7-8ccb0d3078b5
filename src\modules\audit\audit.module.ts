import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditService } from './services/audit.service';
import { ComplianceReportingService } from './services/compliance-reporting.service';
import { LogAggregationService } from './services/log-aggregation.service';
import { RetentionService } from './services/retention.service';
import { AuditController } from './controllers/audit.controller';
import { ComplianceController } from './controllers/compliance.controller';
import { ReportsController } from './controllers/reports.controller';
import { GroupChangeLog } from './entities/group-change-logs.entity';
import { GroupLogService } from './services/group.log.service';
import { MemberEventLog } from './entities/member-event-logs.entity';
import { EventLogService } from './services/member-event.service';

/**
 * Audit Module
 *
 * Handles audit logging, compliance reporting, and data retention
 * for the chat application. Provides comprehensive audit trails
 * for security and compliance requirements.
 */
@Module({
  imports: [TypeOrmModule.forFeature([GroupChangeLog, MemberEventLog])],
  controllers: [AuditController, ComplianceController, ReportsController],
  providers: [
    AuditService,
    ComplianceReportingService,
    LogAggregationService,
    RetentionService,
    GroupLogService,
    EventLogService,
  ],
  exports: [
    AuditService,
    ComplianceReportingService,
    LogAggregationService,
    RetentionService,
    GroupLogService,
    EventLogService,
  ],
})
export class AuditModule {}
