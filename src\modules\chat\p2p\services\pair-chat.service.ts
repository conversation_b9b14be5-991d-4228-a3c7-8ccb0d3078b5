import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryRunner, Repository } from 'typeorm';
import { PairChat } from '../entities/pair-chats.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PairChatService {
  constructor(
    @InjectRepository(PairChat)
    private readonly pairChatRepo: Repository<PairChat>,
  ) {}

  async findOrCreatePairChat(
    member1Id: number,
    member2Id: number,
    queryRunner: QueryRunner,
    keyVersion?: number,
  ): Promise<number> {
    // Ensure consistent ordering
    const [smallerId, largerId] =
      member1Id < member2Id ? [member1Id, member2Id] : [member2Id, member1Id];

    let pairChat = await queryRunner.manager.findOne(PairChat, {
      where: { member1Id: smallerId, member2Id: largerId, isActive: true },
    });

    if (!pairChat) {
      pairChat = queryRunner.manager.create(PairChat, {
        member1Id: smallerId,
        member2Id: largerId,
        currentKeyVersion: keyVersion || 1,
        isActive: true,
      });

      pairChat = await queryRunner.manager.save(PairChat, pairChat);
    }

    return pairChat.id;
  }

  async findPairChatId(
    member1Id: number,
    member2Id: number,
    manager: any,
  ): Promise<number | null> {
    const [minId, maxId] = [member1Id, member2Id].sort((a, b) => a - b);

    const pairChat = await manager.findOne(PairChat, {
      where: { member1Id: minId, member2Id: maxId },
    });

    return pairChat?.id || null;
  }

  /**
   * Find or create PairChat and return the ID
   */
  async findOrCreatePairChatId(
    member1Id: number,
    member2Id: number,
    manager?: EntityManager,
  ): Promise<number> {
    const repository = manager
      ? manager.getRepository(PairChat)
      : this.pairChatRepo;

    // Ensure consistent ordering
    const [smallerId, largerId] = [member1Id, member2Id].sort((a, b) => a - b);

    // Try to find existing
    let pairChat = await repository.findOne({
      where: {
        member1Id: smallerId,
        member2Id: largerId,
      },
    });

    if (!pairChat) {
      // Create new PairChat
      pairChat = repository.create({
        member1Id: smallerId,
        member2Id: largerId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      pairChat = await repository.save(pairChat);
    }

    return pairChat.id;
  }

  /**
   * Get the two member IDs from a PairChat
   */
  async getChatMembers(
    pairChatId: number,
    queryRunner?: QueryRunner,
  ): Promise<{ member1Id: number; member2Id: number }> {
    const manager = queryRunner?.manager || this.pairChatRepo.manager;

    const pairChat = await manager.findOne(PairChat, {
      where: { id: pairChatId },
    });

    if (!pairChat) {
      throw new Error(`PairChat with ID ${pairChatId} not found`);
    }

    return {
      member1Id: pairChat.member1Id,
      member2Id: pairChat.member2Id,
    };
  }

  /**
   * Get PairChat by ID
   */
  async findById(id: number): Promise<PairChat | null> {
    return this.pairChatRepo.findOne({
      where: { id },
    });
  }

  /**
   * Get all PairChats for a member
   */
  async findPairChatsForMember(memberId: number): Promise<PairChat[]> {
    return this.pairChatRepo
      .createQueryBuilder('pc')
      .where('pc.member1Id = :memberId OR pc.member2Id = :memberId', {
        memberId,
      })
      .orderBy('pc.updatedAt', 'DESC')
      .getMany();
  }

  /**
   * Get the other member ID from a PairChat
   */
  async getOtherMemberId(
    pairChatId: number,
    currentMemberId: number,
  ): Promise<number> {
    const pairChat = await this.findById(pairChatId);

    if (!pairChat) {
      throw new Error(`PairChat with ID ${pairChatId} not found`);
    }

    if (pairChat.member1Id === currentMemberId) {
      return pairChat.member2Id;
    } else if (pairChat.member2Id === currentMemberId) {
      return pairChat.member1Id;
    } else {
      throw new Error(
        `Member ${currentMemberId} is not part of PairChat ${pairChatId}`,
      );
    }
  }

  /**
   * Update PairChat last activity
   */
  async updateLastActivity(
    pairChatId: number,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(PairChat)
      : this.pairChatRepo;

    await repository.update(pairChatId, {
      updatedAt: new Date(),
    });
  }

  /**
   * Check if PairChat exists between two members
   */
  async exists(member1Id: number, member2Id: number): Promise<boolean> {
    const [smallerId, largerId] = [member1Id, member2Id].sort((a, b) => a - b);

    const pairChat = await this.pairChatRepo.findOne({
      where: {
        member1Id: smallerId,
        member2Id: largerId,
      },
    });

    return !!pairChat;
  }

  /**
   * Delete a PairChat (usually when both members are deactivated)
   */
  async deletePairChat(pairChatId: number): Promise<void> {
    await this.pairChatRepo.delete(pairChatId);
  }

  /**
   * Get PairChat statistics for a member
   */
  async getMemberPairChatStats(memberId: number): Promise<{
    totalPairChats: number;
    activePairChats: number;
  }> {
    const totalPairChats = await this.pairChatRepo
      .createQueryBuilder('pc')
      .where('pc.member1Id = :memberId OR pc.member2Id = :memberId', {
        memberId,
      })
      .getCount();

    // For active count, you might need to join with sessions or other criteria
    const activePairChats = await this.pairChatRepo
      .createQueryBuilder('pc')
      .innerJoin('session_states', 'ss', 'ss.pair_chat_id = pc.id')
      .where('(pc.member1Id = :memberId OR pc.member2Id = :memberId)', {
        memberId,
      })
      .andWhere('ss.is_active = true')
      .getCount();

    return {
      totalPairChats,
      activePairChats,
    };
  }

  /**
   * Bulk create PairChats for efficiency
   */
  async bulkCreatePairChats(
    memberPairs: Array<{ member1Id: number; member2Id: number }>,
    manager?: EntityManager,
  ): Promise<PairChat[]> {
    const repository = manager
      ? manager.getRepository(PairChat)
      : this.pairChatRepo;

    const pairChatsToCreate: Partial<PairChat>[] = [];
    const existingPairChats = new Set<string>();

    // Check for existing PairChats first
    for (const pair of memberPairs) {
      const [smallerId, largerId] = [pair.member1Id, pair.member2Id].sort(
        (a, b) => a - b,
      );
      const pairKey = `${smallerId}_${largerId}`;

      if (!existingPairChats.has(pairKey)) {
        const existing = await repository.findOne({
          where: {
            member1Id: smallerId,
            member2Id: largerId,
          },
        });

        if (!existing) {
          pairChatsToCreate.push({
            member1Id: smallerId,
            member2Id: largerId,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }

        existingPairChats.add(pairKey);
      }
    }

    if (pairChatsToCreate.length === 0) {
      return [];
    }

    const createdPairChats = repository.create(pairChatsToCreate);
    return await repository.save(createdPairChats);
  }

  /**
   * Clean up orphaned PairChats (where both members are deactivated)
   */
  async cleanupOrphanedPairChats(
    deactivatedMemberIds: number[],
    dryRun: boolean = true,
  ): Promise<number> {
    if (deactivatedMemberIds.length === 0) return 0;

    const orphanedPairChats = await this.pairChatRepo
      .createQueryBuilder('pc')
      .where('pc.member1Id IN (:...deactivatedIds)', {
        deactivatedIds: deactivatedMemberIds,
      })
      .andWhere('pc.member2Id IN (:...deactivatedIds)', {
        deactivatedIds: deactivatedMemberIds,
      })
      .getMany();

    if (!dryRun && orphanedPairChats.length > 0) {
      await this.pairChatRepo.remove(orphanedPairChats);
    }

    return orphanedPairChats.length;
  }

  /**
   * Get PairChat with member details
   */
  async getPairChatWithMembers(pairChatId: number): Promise<PairChat | null> {
    return this.pairChatRepo
      .createQueryBuilder('pc')
      .leftJoinAndSelect('pc.member1', 'member1')
      .leftJoinAndSelect('pc.member2', 'member2')
      .where('pc.id = :pairChatId', { pairChatId })
      .getOne();
  }

  /**
   * Find PairChats that need cleanup (no recent activity)
   */
  async findStalePairChats(
    staleAfter: Date,
    limit: number = 100,
  ): Promise<PairChat[]> {
    return this.pairChatRepo
      .createQueryBuilder('pc')
      .leftJoin('session_states', 'ss', 'ss.pair_chat_id = pc.id')
      .where('pc.updatedAt < :staleAfter', { staleAfter })
      .andWhere('(ss.id IS NULL OR ss.is_active = false)')
      .limit(limit)
      .getMany();
  }
}
