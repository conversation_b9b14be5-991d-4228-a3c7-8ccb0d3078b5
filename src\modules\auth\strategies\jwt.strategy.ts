import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { User } from '../../users/entities/user.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { USER_NOT_FOUND } from '../../../common/constants/error.constants';
import { USER_TYPE } from 'src/common/constants/user-type.constants';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import {
  AdminUser,
  AuthenticatedUser,
  MemberUser,
} from 'src/common/types/authenticate-user.types';
import { AdminUserType } from 'src/common/types/user-type';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(OrgMember)
    private orgMemberRepository: Repository<OrgMember>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        JwtStrategy.extractTokenFromHeaderOrCookie,
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('jwt.secret'),
    });
  }

  private static extractTokenFromHeaderOrCookie(req: Request): string | null {
    // Try Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.split(' ')[1];
    }

    // Fallback: Try cookie
    if (req.cookies?.access_token) {
      return req.cookies.access_token;
    }

    return null;
  }

  async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
    if (
      payload.type === USER_TYPE.PRODUCT_ADMIN ||
      payload.type === USER_TYPE.ORG_ADMIN
    ) {
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        relations: [
          'role',
          ...(payload.type === USER_TYPE.ORG_ADMIN ? ['organization'] : []),
        ],
      });

      if (!user) throw new UnauthorizedException(USER_NOT_FOUND);

      // Map entity to AdminUser type
      const adminUser: AdminUser = {
        id: user.id,
        email: user.email,
        orgId: user.orgId ?? undefined,
        imageUrl: user.imageUrl ?? undefined,
        type: payload.type as AdminUserType,
        roleId: user.roleId,
        username: user.username,
        organization: user.organization
          ? { id: user.organization.id, name: user.organization.name }
          : undefined,
        encryptedAdminSecretKey: user.encryptedAdminSecretKey ?? undefined,
        adminSecretKeyNonce: user.adminSecretKeyNonce ?? undefined,
        adminSecretKeySalt: user.adminSecretKeySalt ?? undefined,
      };

      return adminUser;
    } else if (payload.type === USER_TYPE.ORG_MEMBER) {
      const member = await this.orgMemberRepository.findOne({
        where: { id: payload.sub },
        relations: ['organization', 'identityKey'],
      });

      if (!member) throw new UnauthorizedException(USER_NOT_FOUND);

      // Map entity to MemberUser type
      const memberUser: MemberUser = {
        id: member.id,
        email: member.email,
        orgId: member.orgId,
        imageUrl: member.imageUrl ?? undefined,
        type: USER_TYPE.ORG_MEMBER,
        name: member.name,
        organization: {
          id: member.organization.id,
          name: member.organization.name,
          imageUrl: member.organization.imageUrl,
        },
        encryptedPrivateKey: member.identityKey.encryptedPrivateKey,
        publicKey: member.identityKey.publicKey ?? '',
        createdBy: member.createdBy,
      };

      return memberUser;
    }
    throw new UnauthorizedException(USER_NOT_FOUND);
  }
}
