import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Query<PERSON><PERSON>ner, Repository } from 'typeorm';
import { SessionStateEntity } from '../entities/session-state.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';

export interface SessionInfo {
  sessionId: string;
  pairChatId: number;
  otherMemberId: number;
  groupContextIds: number[];
  lastActiveAt?: Date | null;
  wasReactivated: boolean;
  isActive: boolean;
}

export interface CreateSessionOptions {
  groupContextIds?: number[];
}

/**
 * Session State Service
 *
 * Manages cryptographic sessions between users.
 * Handles session establishment, maintenance, and cleanup
 * for secure peer-to-peer communication.
 */
@Injectable()
export class SessionStateService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @InjectRepository(SessionStateEntity)
    private readonly sessionRepo: Repository<SessionStateEntity>,
  ) {}

  async findActiveByPairChat(
    manager: Enti<PERSON><PERSON><PERSON><PERSON>,
    pairChatId: number,
  ): Promise<SessionStateEntity | null> {
    return manager.findOne(SessionStateEntity, {
      where: { pairChatId, isActive: true },
      relations: ['pairChat'],
    });
  }

  async findByPairChat(
    manager: EntityManager,
    pairChatId: number,
    includeInactive: boolean = false,
  ): Promise<SessionStateEntity | null> {
    const where: any = { pairChatId };
    if (!includeInactive) {
      where.isActive = true;
    }

    return manager.findOne(SessionStateEntity, {
      where,
      relations: ['pairChat'],
      order: { createdAt: 'DESC' }, // Get most recent if multiple
    });
  }

  async saveSession(
    manager: EntityManager,
    session: SessionStateEntity,
  ): Promise<SessionStateEntity> {
    return manager.save(SessionStateEntity, session);
  }

  async updateSession(
    manager: EntityManager,
    sessionId: string,
    updates: Partial<SessionStateEntity>,
  ): Promise<void> {
    await manager.update(SessionStateEntity, sessionId, {
      ...updates,
      updatedAt: new Date(),
    });
  }

  /**
   * Create or update session between members
   */
  async createSessionBetweenMembers(
    pairChatId: number,
    queryRunner: QueryRunner,
    groupContextId?: number,
    options: CreateSessionOptions = {},
  ): Promise<SessionStateEntity> {
    const existingSession = await queryRunner.manager.findOne(
      SessionStateEntity,
      {
        where: { pairChatId, isActive: true },
      },
    );

    if (existingSession) {
      // Update group context if provided
      if (groupContextId) {
        const currentGroups = existingSession.groupContextIds || [];
        if (!currentGroups.includes(groupContextId)) {
          existingSession.groupContextIds = [...currentGroups, groupContextId];
          existingSession.lastActiveAt = new Date();
          await queryRunner.manager.save(SessionStateEntity, existingSession);
        }
      }
      return existingSession;
    }

    // Create new session
    const session = queryRunner.manager.create(SessionStateEntity, {
      pairChatId,
      isActive: true,
      lastActiveAt: new Date(),
      groupContextIds: groupContextId
        ? [groupContextId]
        : options.groupContextIds || [],
      wasReactivated: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return await queryRunner.manager.save(SessionStateEntity, session);
  }

  /**
   * Get active sessions for a member with detailed information
   */
  async getActiveSessionsForMember(memberId: number): Promise<SessionInfo[]> {
    const sessions = await this.sessionRepo
      .createQueryBuilder('s')
      .innerJoinAndSelect('s.pairChat', 'pc')
      .where('(pc.member1Id = :memberId OR pc.member2Id = :memberId)', {
        memberId,
      })
      .andWhere('s.isActive = true')
      .orderBy('s.lastActiveAt', 'DESC')
      .getMany();

    return sessions.map((s) => {
      const otherMemberId =
        s.pairChat.member1Id === memberId
          ? s.pairChat.member2Id
          : s.pairChat.member1Id;

      return {
        sessionId: s.id,
        pairChatId: s.pairChatId,
        otherMemberId,
        groupContextIds: s.groupContextIds || [],
        lastActiveAt: s.lastActiveAt,
        wasReactivated: s.wasReactivated,
        isActive: s.isActive,
      };
    });
  }

  /**
   * Get session information by ID
   */
  async getSession(sessionId: string): Promise<SessionStateEntity | null> {
    return this.sessionRepo.findOne({
      where: { id: sessionId },
      relations: ['pairChat'],
    });
  }

  /**
   * Get session by pair chat ID
   */
  async getSessionByPairChat(
    pairChatId: number,
  ): Promise<SessionStateEntity | null> {
    return this.sessionRepo.findOne({
      where: { pairChatId, isActive: true },
      relations: ['pairChat'],
    });
  }

  /**
   * Deactivate a session (soft delete)
   */
  async deactivateSession(
    sessionId: string,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    await repository.update(sessionId, {
      isActive: false,
      deactivatedAt: new Date(),
      updatedAt: new Date(),
    });
  }

  /**
   * Reactivate a deactivated session
   */
  async reactivateSession(
    sessionId: string,
    groupContextIds: number[],
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    await repository.update(sessionId, {
      isActive: true,
      wasReactivated: true,
      lastActiveAt: new Date(),
      groupContextIds,
      deactivatedAt: null,
      updatedAt: new Date(),
    });
  }

  /**
   * Delete a session permanently
   */
  async deleteSession(sessionId: string): Promise<void> {
    await this.sessionRepo.delete(sessionId);
  }

  /**
   * List all sessions for a user (active and inactive)
   */
  async getUserSessions(
    userId: number,
    includeInactive: boolean = false,
  ): Promise<SessionInfo[]> {
    const queryBuilder = this.sessionRepo
      .createQueryBuilder('s')
      .innerJoinAndSelect('s.pairChat', 'pc')
      .where('(pc.member1Id = :userId OR pc.member2Id = :userId)', {
        userId,
      });

    if (!includeInactive) {
      queryBuilder.andWhere('s.isActive = true');
    }

    const sessions = await queryBuilder
      .orderBy('s.lastActiveAt', 'DESC')
      .getMany();

    return sessions.map((s) => {
      const otherMemberId =
        s.pairChat.member1Id === userId
          ? s.pairChat.member2Id
          : s.pairChat.member1Id;

      return {
        sessionId: s.id,
        pairChatId: s.pairChatId,
        otherMemberId,
        groupContextIds: s.groupContextIds || [],
        lastActiveAt: s.lastActiveAt,
        wasReactivated: s.wasReactivated,
        isActive: s.isActive,
      };
    });
  }

  /**
   * Check if active session exists
   */
  async hasActiveSession(
    pairChatId: number,
    queryRunner?: QueryRunner,
  ): Promise<boolean> {
    const manager = queryRunner?.manager || this.dataSource.manager;

    const session = await manager.findOne(SessionStateEntity, {
      where: { pairChatId, isActive: true },
    });

    return !!session;
  }

  /**
   * Check if session exists between two users
   */
  async sessionExists(
    user1Id: number,
    user2Id: number,
    activeOnly: boolean = true,
  ): Promise<boolean> {
    const where: any = {};
    if (activeOnly) {
      where.isActive = true;
    }

    const session = await this.sessionRepo
      .createQueryBuilder('s')
      .innerJoin('s.pairChat', 'pc')
      .where(
        '((pc.member1Id = :user1Id AND pc.member2Id = :user2Id) OR ' +
          '(pc.member1Id = :user2Id AND pc.member2Id = :user1Id))',
        { user1Id, user2Id },
      )
      .andWhere(activeOnly ? 's.isActive = true' : '1=1')
      .getOne();

    return !!session;
  }

  /**
   * Update session message count and last activity
   */
  async updateSessionActivity(
    sessionId: string,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    const updateData: any = {
      lastActiveAt: new Date(),
      updatedAt: new Date(),
    };

    await repository.update(sessionId, updateData);
  }

  /**
   * Update session group context
   */
  async updateSessionGroupContext(
    sessionId: string,
    groupContextIds: number[],
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    await repository.update(sessionId, {
      groupContextIds,
      updatedAt: new Date(),
    });
  }

  /**
   * Add group to session context
   */
  async addGroupToSessionContext(
    sessionId: string,
    groupId: number,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    const session = await repository.findOne({ where: { id: sessionId } });
    if (session) {
      const currentGroups = session.groupContextIds || [];
      if (!currentGroups.includes(groupId)) {
        await repository.update(sessionId, {
          groupContextIds: [...currentGroups, groupId],
          updatedAt: new Date(),
        });
      }
    }
  }

  /**
   * Remove group from session context
   */
  async removeGroupFromSessionContext(
    sessionId: string,
    groupId: number,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    const session = await repository.findOne({ where: { id: sessionId } });
    if (session) {
      const currentGroups = session.groupContextIds || [];
      const updatedGroups = currentGroups.filter((id) => id !== groupId);

      await repository.update(sessionId, {
        groupContextIds: updatedGroups,
        updatedAt: new Date(),
      });
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(maxAge: number): Promise<number> {
    const cutoffDate = new Date(Date.now() - maxAge);

    const expiredSessions = await this.sessionRepo
      .createQueryBuilder()
      .where('isActive = false')
      .andWhere('deactivatedAt < :cutoffDate', { cutoffDate })
      .getMany();

    if (expiredSessions.length > 0) {
      await this.sessionRepo.remove(expiredSessions);
    }

    return expiredSessions.length;
  }

  /**
   * Validate session integrity
   */
  async validateSession(sessionId: string): Promise<boolean> {
    const session = await this.sessionRepo.findOne({
      where: { id: sessionId },
      relations: ['pairChat'],
    });

    if (!session) return false;
    if (!session.pairChat) return false;
    if (!session.isActive && !session.deactivatedAt) return false;

    return true;
  }

  /**
   * Get sessions by group context
   */
  async getSessionsByGroupContext(
    groupId: number,
  ): Promise<SessionStateEntity[]> {
    return this.sessionRepo
      .createQueryBuilder('s')
      .where('s.groupContextIds @> :groupId', { groupId: [groupId] })
      .andWhere('s.isActive = true')
      .getMany();
  }

  /**
   * Bulk update sessions
   */
  async bulkUpdateSessions(
    updates: Array<{
      sessionId: string;
      updates: Partial<SessionStateEntity>;
    }>,
    manager?: EntityManager,
  ): Promise<void> {
    const repository = manager
      ? manager.getRepository(SessionStateEntity)
      : this.sessionRepo;

    for (const { sessionId, updates: fields } of updates) {
      await repository.update(sessionId, {
        ...fields,
        updatedAt: new Date(),
      });
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    inactiveSessions: number;
    reactivatedSessions: number;
  }> {
    const totalSessions = await this.sessionRepo.count();
    const activeSessions = await this.sessionRepo.count({
      where: { isActive: true },
    });
    const inactiveSessions = await this.sessionRepo.count({
      where: { isActive: false },
    });
    const reactivatedSessions = await this.sessionRepo.count({
      where: { wasReactivated: true, isActive: true },
    });

    return {
      totalSessions,
      activeSessions,
      inactiveSessions,
      reactivatedSessions,
    };
  }
}
