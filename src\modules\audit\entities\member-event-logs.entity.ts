import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { MemberEventType } from '../enums/member-event.enum';

@Entity('member_event_logs')
@Index('idx_member_seq', ['memberId', 'memberSeq'], { unique: true })
@Index('idx_member_created', ['memberId', 'createdAt'])
@Index('idx_source_reference', ['sourceTable', 'sourceId'])
export class MemberEventLog {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ name: 'member_id', type: 'int' })
  memberId: number;

  @Column({ name: 'member_seq', type: 'bigint' })
  memberSeq: number;

  @Column({
    name: 'event_type',
    type: 'enum',
    enum: MemberEventType,
  })
  eventType: MemberEventType;

  @Column({ name: 'source_table', type: 'varchar', length: 50, nullable: true })
  sourceTable?: string;

  @Column({ name: 'source_id', type: 'bigint', nullable: true })
  sourceId?: string;

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @ManyToOne(() => OrgMember, { nullable: false })
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
