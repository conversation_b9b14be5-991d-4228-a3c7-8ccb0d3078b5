import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { MemberVerifiedEvent } from 'src/common/events/member-verified.event';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { GroupMembersService } from 'src/modules/groups/services/group-member.service';
import { MessageThreadService } from 'src/modules/messages/services/message-threads.service';

@Injectable()
export class MemberEventsHandler {
  private readonly logger = new Logger(MemberEventsHandler.name);
  constructor(
    private readonly notificationService: NotificationService,
    private readonly groupMembersService: GroupMembersService,
    private readonly messageThreadsService: MessageThreadService,
  ) {}

  @OnEvent(EVENT_NAMES.MEMBER_VERIFIED, { async: true })
  async handleMemberVerified(event: MemberVerifiedEvent) {
    this.logger.log(
      `Member ${event.payload.memberId} verified (org: ${event.payload.orgId})`,
    );

    const { memberId, isVerified, timestamp } = event.payload;

    const associatedMemberIds =
      await this.groupMembersService.getVerifiedAssociatedMemberIds(memberId);

    // Run all notifications in parallel
    await Promise.all(
      associatedMemberIds.map(async (associatedMemberId) => {
        const privateThreadData =
          await this.messageThreadsService.findOrCreateThread(
            memberId,
            ChatType.PRIVATE,
            associatedMemberId,
          );

        const payload = {
          userId: memberId,
          isVerified: Boolean(isVerified),
          timestamp,
          privateThreadsIsActive: Boolean(privateThreadData.canSendMessages),
        };

        await this.notificationService.broadcastToMemberWithAck(
          associatedMemberId,
          EVENT_NAMES.MEMBER_VERIFIED,
          payload,
        );
      }),
    );
  }
}
