// src/users/users.service.ts
import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { User } from '../entities/user.entity';
import { Organization } from '../../organization/entities/organization.entity';
import { CreateUserDto } from '../dto/create-users.dto';
import * as argon2 from 'argon2';
import { MailService } from '../../../core/mail/mail.service';
import {
  generatePassword,
  generateUsername,
} from '../../../common/utils/credentials.util';
import { UpdateUserDto } from '../dto/update.users.dto';
import { EncryptionService } from '../../../core/encryption/encryption.service';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { UpdateOrganizationDto } from '../../organization/dto';
import { OrganizationsService } from 'src/modules/organization/services/organizations.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    private organizationsService: OrganizationsService,
    private dataSource: DataSource,
    private mailService: MailService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async findOne(username: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { username },
      relations: ['role', 'organization'],
    });
    if (!user) {
      throw new NotFoundException(`User with username ${username} not found`);
    }
    return user;
  }

  async findById(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { id },
      relations: ['role', 'organization'],
    });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findByMail(mail: string): Promise<User | null> {
    const user = await this.usersRepository.findOne({
      where: { email: mail },
    });
    return user;
  }

  async findByRoleId(roleId: number, orgId: number): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { roleId, orgId },
      select: ['publicKey'],
    });
    if (!user) {
      throw new NotFoundException(`User with roleId ${roleId} not found`);
    }
    return user;
  }

  async createWithOrganization(createUserDto: CreateUserDto): Promise<User> {
    // Check if user with email already exists
    const existingUser = await this.usersRepository.findOne({
      where: [{ email: createUserDto.email }],
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Generate username and password if not provided
    const username =
      createUserDto.username || generateUsername(createUserDto.email);
    const rawPassword = createUserDto.password || generatePassword();

    // Check if generated username exists
    const existingUsername = await this.usersRepository.findOne({
      where: [{ username }],
    });

    // If username exists, append a random number
    const finalUsername = existingUsername
      ? `${username}${Math.floor(1000 + Math.random() * 9000)}`
      : username;

    // Start transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Hash the password
      const hashedPassword = await argon2.hash(rawPassword);

      let organization: Organization | null = null;

      // Create organization if provided
      if (createUserDto.organization) {
        organization = await this.organizationsService.create(
          createUserDto.organization,
          createUserDto.fileUrl,
          queryRunner,
        );
      }

      // Create user
      const user = this.usersRepository.create({
        username: finalUsername,
        email: createUserDto.email,
        password: hashedPassword,
        roleId: createUserDto.roleId,
        orgId: organization?.id,
      });

      if (createUserDto.roleId === 2) {
        // 1. Generate key pair
        const { secretKey, publicKey } =
          this.encryptionService.generateSigningKeyPair();

        // 2. Encrypt the secret key using the user's raw password
        const encryptedResult = this.encryptionService.encryptSecretKey(
          secretKey,
          rawPassword,
        );

        user.encryptedAdminSecretKey = encryptedResult.encryptedData;
        user.adminSecretKeyNonce = encryptedResult.nonce;
        user.adminSecretKeySalt = encryptedResult.salt;
        user.publicKey = publicKey;
      }

      await queryRunner.manager.save(user);

      // Send email with credentials if organization is created
      if (organization) {
        await this.mailService.sendStaffCredentials(
          createUserDto.email,
          finalUsername,
          rawPassword,
          organization.name,
          organization.id,
        );
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      // Remove password from response
      const userResponse = { ...user } as Partial<User>;
      delete userResponse.password;

      return user;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      if (error.code === '23505') {
        // PostgreSQL unique violation code
        throw new ConflictException(
          'User or organization with these details already exists',
        );
      }

      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  async updateWithOrganization(
    id: number,
    updateOrganizationDto: UpdateOrganizationDto,
  ): Promise<Organization> {
    // Extract adminEmail and fileUrl first to avoid passing them to organization update
    const { adminEmail, fileUrl, ...organizationUpdateData } =
      updateOrganizationDto;

    // Check if organization exists first
    const organization = await this.organizationsService.findOne(id);

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Check for name conflict if name is being updated
    if (
      updateOrganizationDto.name &&
      updateOrganizationDto.name !== organization.name
    ) {
      const existingOrg = await this.organizationsService.findByName(
        updateOrganizationDto.name,
      );

      if (existingOrg) {
        throw new ConflictException(
          'Organization with this name already exists',
        );
      }
    }

    // Start transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.update(Organization, id, {
        ...organizationUpdateData,
        imageUrl: fileUrl,
      });

      // Handle admin update
      if (adminEmail) {
        // Find existing admin of this organization
        const existingAdmin = organization.admins?.find(
          (user) => user.roleId === 2,
        );

        if (!existingAdmin) {
          throw new NotFoundException(
            'No existing admin found for this organization',
          );
        }

        // Check if email belongs to the same existing admin
        if (
          adminEmail !== existingAdmin.email &&
          (await this.usersRepository.findOne({ where: { email: adminEmail } }))
        ) {
          throw new ConflictException('User with this email already exists');
        }

        if (adminEmail !== existingAdmin.email) {
          // Generate new username & password
          const username = generateUsername(adminEmail);
          const rawPassword = generatePassword();

          // Ensure new username is unique
          const usernameExists = await this.usersRepository.findOne({
            where: { username },
          });

          const finalUsername = usernameExists
            ? `${username}${Math.floor(1000 + Math.random() * 9000)}`
            : username;

          const hashedPassword = await argon2.hash(rawPassword);

          // Generate key pair and encrypt
          const { secretKey, publicKey } =
            this.encryptionService.generateSigningKeyPair();

          const encryptedResult = this.encryptionService.encryptSecretKey(
            secretKey,
            rawPassword,
          );

          // Update the existing admin user
          await queryRunner.manager.update(User, existingAdmin.id, {
            username: finalUsername,
            email: adminEmail,
            password: hashedPassword,
            encryptedAdminSecretKey: encryptedResult.encryptedData,
            adminSecretKeyNonce: encryptedResult.nonce,
            adminSecretKeySalt: encryptedResult.salt,
            publicKey,
          });

          // Send credentials to updated admin
          await this.mailService.sendStaffCredentials(
            adminEmail,
            finalUsername,
            rawPassword,
            updateOrganizationDto.name || organization.name,
            id,
          );
        }
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      const updatedOrg = await this.organizationsService.findOne(id);

      if (!updatedOrg) {
        throw new NotFoundException(
          `Organization with ID ${id} not found after update`,
        );
      }

      return updatedOrg;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error.code === '23505') {
        throw new ConflictException('Email already exists in the system');
      }

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    await this.usersRepository.update(id, {
      username: updateUserDto.username,
      email: updateUserDto.email,
      imageUrl: updateUserDto.fileUrl,
    });

    // Return the updated member

    const updatedUser = await this.usersRepository.findOne({
      where: { id },
    });
    if (!updatedUser) {
      throw new NotFoundException(`Member with ID ${id} not found`);
    }
    return updatedUser;
  }

  async changePassword(
    id: number,
    changePasswordDto: ChangePasswordDto,
  ): Promise<Partial<User> | { message: string }> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    if (
      changePasswordDto.newPassword !== changePasswordDto.confirmNewPassword
    ) {
      throw new BadRequestException(
        'New password and confirm new password do not match',
      );
    }

    // Verify current password
    const isPasswordValid = await argon2.verify(
      user.password,
      changePasswordDto.currentPassword,
    );

    if (!isPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    const hashedPassword = await argon2.hash(changePasswordDto.newPassword);
    if (user.roleId === 1) {
      await this.usersRepository.update(id, { password: hashedPassword });
      return { message: 'Password updated successfully' };
    }

    let decryptedSecretKey: string;
    try {
      decryptedSecretKey = this.encryptionService.decryptSecretKey(
        {
          encryptedData: user.encryptedAdminSecretKey!,
          nonce: user.adminSecretKeyNonce!,
          salt: user.adminSecretKeySalt!,
        },
        changePasswordDto.currentPassword,
      );
    } catch (err) {
      throw new BadRequestException('Failed to decrypt admin secret key');
    }

    // ✅ STEP 2: Re-encrypt secret key with new password
    const reEncrypted = this.encryptionService.encryptSecretKey(
      decryptedSecretKey,
      changePasswordDto.newPassword,
    );

    // ✅ STEP 4: Update password + re-encrypted key data
    await this.usersRepository.update(id, {
      password: hashedPassword,
      encryptedAdminSecretKey: reEncrypted.encryptedData,
      adminSecretKeyNonce: reEncrypted.nonce,
      adminSecretKeySalt: reEncrypted.salt,
    });

    return {
      encryptedAdminSecretKey: reEncrypted.encryptedData,
      adminSecretKeyNonce: reEncrypted.nonce,
      adminSecretKeySalt: reEncrypted.salt,
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    const result = await this.usersRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return { message: 'User deleted successfully' };
  }
}
