// src/modules/security/entities/chain-key-state.entity.ts
import { OrgMember } from '../../members/entities/org-member.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('chain_key_states')
@Index(['sessionId', 'ownerMemberId', 'chainType'], { unique: true })
export class ChainKeyStateEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  sessionId: string;

  @Column()
  @Index()
  ownerMemberId: number;

  @Column()
  chainType: 'sending' | 'receiving';

  @Column({ type: 'int', default: 0 })
  messageIndex: number;

  @Column({ type: 'int', default: 0 })
  chainLength: number;

  @Column({ default: false })
  isStale: boolean;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @Column({ nullable: true, type: 'timestamp' })
  lastUsedAt?: Date;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.chainKeyStates)
  @JoinColumn({ name: 'ownerMemberId' })
  owner: OrgMember;
}
