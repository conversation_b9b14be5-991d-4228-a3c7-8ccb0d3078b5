import { Injectable, Logger } from '@nestjs/common';
import { Group } from '../../../groups/entities/group.entity';
import { StorageService } from 'src/core/storage/storage.service';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { GroupEncryptionService } from 'src/modules/groups/services/group-encryption.service';
import { MessageThreadService } from '../../../messages/services/message-threads.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { MembersService } from 'src/modules/members/services/members.service';
import { GroupMembersService } from 'src/modules/groups/services/group-member.service';
import { getMessagePreview } from 'src/common/utils/message-preview.util';
import { PrivateMessage } from 'src/modules/messages/entities/private-message.entity';
import { PrivateMessageService } from 'src/modules/messages/services/pvt-messages.service';

@Injectable()
export class GroupNotificationService {
  private readonly logger = new Logger(GroupNotificationService.name);

  constructor(
    private readonly notificationService: NotificationService,
    private readonly storageService: StorageService,
    private readonly groupEncryptionService: GroupEncryptionService,
    private readonly messageThreadService: MessageThreadService,
    private readonly memberService: MembersService,
    private readonly groupMemberService: GroupMembersService,
    private readonly privateMessageService: PrivateMessageService,
  ) {}

  async emitGroupInfoToNewMembers(
    group: Group,
    memberIds: number[],
    allMembers: any[],
    // latestSeq: number,
  ) {
    // Process each new member individually
    for (const memberId of memberIds) {
      try {
        // Get encrypted key for this member
        const encryptedKey =
          await this.groupEncryptionService.getActiveGroupKeyForMember(
            group.id,
            memberId,
          );

        // Get member's private threads (for sync state)
        const privateThreads = await this.messageThreadService.getMemberThreads(
          memberId,
          ChatType.PRIVATE,
        );

        const groupThread = await this.messageThreadService.findOrCreateThread(
          memberId,
          ChatType.GROUP,
          group.id,
        );

        // Convert to private threads data format
        const privateThreadsData = await Promise.all(
          privateThreads.map(async (thread) => {
            const otherMember =
              await this.memberService.findOneWithCryptoStatus(thread.targetId);
            if (!otherMember) return null;

            const participantIds = [memberId, thread.targetId].sort(
              (a, b) => a - b,
            );
            const privateChatId = `private_${participantIds[0]}_${participantIds[1]}`;
            let lastMessage: PrivateMessage | null = null;
            if (thread.lastMessageId) {
              lastMessage = await this.privateMessageService.findOneMessage(
                thread.lastMessageId,
              );
            }

            return {
              id: privateChatId,
              threadId: thread.id,
              type: 'private',
              participantId: thread.targetId,
              unreadCount: thread.unreadCount,
              isMuted: thread.isMuted,
              isPinned: thread.isPinned,
              lastUpdated: thread.updatedAt,
              syncState: {
                lastSyncedSeq: thread.syncState?.lastSyncedSeq || 0,
                lastAckedSeq: thread.syncState?.lastAckedSeq || 0,
                hasPending: thread.syncState?.hasPending || false,
              },
              lastMessageSeq: 0, // No messages yet for new member
              lastSeenSeq: thread.syncState?.lastAckedSeq || 0,
              createdAt: thread.createdAt,
              updatedAt: thread.updatedAt,
              welcomeMessage: thread.welcomeMessage || null,
              preview: lastMessage ? getMessagePreview(lastMessage) : '',
              isActive: Boolean(
                thread.canSendMessages &&
                  otherMember.isVerified &&
                  otherMember.hasSignedPreKey,
              ),
            };
          }),
        );

        // Get this member's group membership details
        const membership = await this.groupMemberService.findActiveMembership(
          memberId,
          group.id,
        );

        // Prepare users array (all members in this group)
        const users = await Promise.all(
          allMembers.map(async (member) => ({
            userId: member.id,
            name: member.name,
            imageUrl: member.imageUrl, // Already has signed URL from getAllMembersWithSignedUrls
            phoneNo: member.phoneNo,
            isVerified: member.isVerified,
            isCurrentUser: member.id === memberId,
          })),
        );

        // Prepare group members array (all group memberships)
        const groupMembers = allMembers.map((member) => ({
          id: member.groupMemberships?.find((gm) => gm.groupId === group.id)
            ?.id,
          groupId: group.id,
          userId: member.id,
          joinedAt: member.groupMemberships?.find(
            (gm) => gm.groupId === group.id,
          )?.joinedAt,
          isMute:
            member.groupMemberships?.find((gm) => gm.groupId === group.id)
              ?.isMute || false,
          isActive: true,
        }));

        // Fix: Check if group.imageUrl exists before generating signed URL
        const signedUrl =
          group.imageUrl && group.imageUrl.trim().length > 0
            ? await this.storageService.generateSignedUrl(
                group.imageUrl,
                24 * 60 * 60,
              )
            : null;

        const payload = {
          // Encryption keys
          groupId: group.id,
          encryptedGroupKey: encryptedKey?.encryptedGroupKey || '',
          keyVersion: encryptedKey?.keyVersion || 0,

          // Data arrays matching getUserGroups structure
          users: users,
          groupMembers: groupMembers,
          privateThreads: privateThreadsData.filter(
            (thread) => thread !== null,
          ),

          // Sync state
          syncState: {
            lastSyncedSeq: 0,
            lastAckedSeq: 0,
            hasPending: false,
          },
          // latestSeq: latestSeq,

          // Group info
          groupInfo: {
            id: group.id,
            name: group.name,
            imageUrl: signedUrl,
            memberCount: allMembers.length,
            isActive: group.isActive,
            createdAt: group.createdAt,
            updatedAt: group.updatedAt,
            welcomeMessage: groupThread?.welcomeMessage || null,
          },
        };

        await this.notificationService.broadcastToMemberWithAck(
          memberId,
          EVENT_NAMES.GROUP_INFO,
          payload,
        );
      } catch (error) {
        this.logger.error(
          `Failed to emit group info to member ${memberId}:`,
          error,
        );
      }
    }
  }

  async emitGroupUpdatesToExistingMembers(
    group: Group,
    memberIds: number[],
    changes: {
      newMembers: any[];
      leftMembers: any[];
      newKeyVersion: number | null;
    },
    // latestSeq: number,
  ) {
    for (const memberId of memberIds) {
      try {
        // Get member's private threads for sync state
        const privateThreads = await this.messageThreadService.getMemberThreads(
          memberId,
          ChatType.PRIVATE,
        );
        const groupThread = await this.messageThreadService.findOrCreateThread(
          memberId,
          ChatType.GROUP,
          group.id,
        );
        // Convert to private threads data format
        const privateThreadsData = await Promise.all(
          privateThreads.map(async (thread) => {
            const otherMember =
              await this.memberService.findOneWithCryptoStatus(thread.targetId);
            if (!otherMember) return null;

            const participantIds = [memberId, thread.targetId].sort(
              (a, b) => a - b,
            );
            const privateChatId = `private_${participantIds[0]}_${participantIds[1]}`;

            let lastMessage: PrivateMessage | null = null;
            if (thread.lastMessageId) {
              lastMessage = await this.privateMessageService.findOneMessage(
                thread.lastMessageId,
              );
            }

            return {
              id: privateChatId,
              threadId: thread.id,
              type: 'private',
              participantId: thread.targetId,
              unreadCount: thread.unreadCount,
              isMuted: thread.isMuted,
              isPinned: thread.isPinned,
              lastUpdated: thread.updatedAt,
              syncState: {
                lastSyncedSeq: thread.syncState?.lastSyncedSeq || 0,
                lastAckedSeq: thread.syncState?.lastAckedSeq || 0,
                hasPending: thread.syncState?.hasPending || false,
              },
              lastMessageSeq: 0,
              lastSeenSeq: thread.syncState?.lastAckedSeq || 0,
              createdAt: thread.createdAt,
              updatedAt: thread.updatedAt,
              welcomeMessage: thread.welcomeMessage || null,
              preview: lastMessage ? getMessagePreview(lastMessage) : '',
              isActive: Boolean(
                thread.canSendMessages &&
                  otherMember.isVerified &&
                  otherMember.hasSignedPreKey,
              ),
            };
          }),
        );

        // Prepare encryption key payload if needed
        let encryptionPayload = {};
        if (changes.newKeyVersion !== null) {
          const encryptedKey =
            await this.groupEncryptionService.getActiveGroupKeyForMember(
              group.id,
              memberId,
            );

          if (encryptedKey) {
            encryptionPayload = {
              encryptedGroupKey: encryptedKey.encryptedGroupKey,
              keyVersion: changes.newKeyVersion,
            };
          }
        }

        // Prepare new members data (users + groupMembers format)
        const newUsersData = changes.newMembers.map((member) => ({
          userId: member.id,
          name: member.name,
          imageUrl: member.imageUrl, // Already has signed URL
          phoneNo: member.phoneNo,
          isVerified: member.isVerified,
          isCurrentUser: false,
        }));

        const newGroupMembersData = changes.newMembers.map((member) => ({
          id: member.groupMemberships?.find((gm) => gm.groupId === group.id)
            ?.id,
          groupId: group.id,
          userId: member.id,
          joinedAt:
            member.groupMemberships?.find((gm) => gm.groupId === group.id)
              ?.joinedAt || new Date(),
          isMute: false,
          isActive: true,
        }));

        // Prepare left members data (minimal info)
        const leftMembersData = changes.leftMembers.map((member) => ({
          memberId: member.id,
          groupId: group.id,
          leftAt: new Date(),
        }));

        const groupMemberDetails =
          await this.groupMemberService.getGroupMemberDetails(group.id);

        const payload = {
          // Group identification
          groupId: group.id,
          memberCount: groupMemberDetails.members.length,
          ...encryptionPayload,
          newMembers: {
            users: newUsersData,
            groupMembers: newGroupMembersData,
          },
          leftMembers: leftMembersData,
          privateThreads: privateThreadsData.filter(
            (thread) => thread !== null,
          ),
          syncState: {
            lastSyncedSeq: groupThread.syncState?.lastSyncedSeq || 0,
            lastAckedSeq: groupThread.syncState?.lastAckedSeq || 0,
            hasPending: groupThread.syncState?.hasPending || false,
          },
          // latestSeq: latestSeq,
          timestamp: new Date(),
        };

        await this.notificationService.broadcastToMemberWithAck(
          memberId,
          EVENT_NAMES.GROUP_UPDATE,
          payload,
        );
      } catch (error) {
        this.logger.error(
          `Failed to emit group updates to member ${memberId}:`,
          error,
        );
      }
    }
  }
}
