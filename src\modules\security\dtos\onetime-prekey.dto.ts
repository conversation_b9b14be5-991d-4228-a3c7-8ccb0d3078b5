import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class OneTimePreKeyDto {
  @ApiProperty({ description: 'Unique keyId for one-time prekey' })
  @IsNumber()
  keyId: number;

  @ApiProperty({ description: 'Public key for one-time prekey' })
  @IsString()
  publicKey: string;
}

export class OneTimePreKeyArrayDto {
  @ApiProperty({ type: [OneTimePreKeyDto], description: 'Array of OTPs' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OneTimePreKeyDto)
  keys: OneTimePreKeyDto[];
}
