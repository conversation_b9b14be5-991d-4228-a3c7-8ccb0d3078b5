import { User } from '../../users/entities/user.entity';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { GroupChangeLog } from '../../audit/entities/group-change-logs.entity';
import { GroupMember } from './group-member.entity';
import { GroupMessage } from 'src/modules/messages/entities/group-message.entity';
import { GroupEncryptionKey } from './group-encryption-keys.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@Entity('groups')
@Index(['orgId', 'name'], { unique: true })
export class Group {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'org_id' })
  orgId: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'last_seq', type: 'bigint', default: 0 })
  lastSeq: number;

  @Column({ name: 'current_key_version', default: 1 })
  currentKeyVersion: number;

  @Column({ name: 'created_by' })
  createdBy: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy: number | null;

  @ManyToOne(() => Organization, (org) => org.groups)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @ManyToOne(() => User, (user) => user.createdGroups)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, (user) => user.deletedGroups, { nullable: true })
  @JoinColumn({ name: 'deleted_by' })
  deleter: User;

  @OneToMany(() => GroupMember, (member) => member.group)
  members: GroupMember[];

  @OneToMany(() => GroupMessage, (message) => message.group)
  messages: GroupMessage[];

  @OneToMany(() => GroupEncryptionKey, (encryptionKey) => encryptionKey.group)
  encryptionKeys: GroupEncryptionKey[];

  @OneToMany(() => GroupChangeLog, (changeLog) => changeLog.group)
  changeLogs: GroupChangeLog[];
}
