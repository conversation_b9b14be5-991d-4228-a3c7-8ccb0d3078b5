import { GroupMessage } from '../../messages/entities/group-message.entity';
import { PrivateMessage } from '../../messages/entities/private-message.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { MediaEncryptionKey } from './media-encryption-key.entity';

@Entity('media_files')
export class MediaFile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  fileUrl: string;

  @Column({ name: 'file_type' })
  fileType: string; // image, video, audio, document

  @Column({ name: 'file_name', nullable: true })
  filename: string;

  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ nullable: true })
  duration: number; // for audio/video

  @Column({ nullable: true })
  width: number;

  @Column({ nullable: true })
  height: number;

  @Column({ name: 'thumbnail_url', nullable: true })
  thumbnailUrl: string;

  @Column({ name: 'waveform_data', type: 'jsonb', nullable: true })
  waveformData: number[];

  @Column({ name: 'extra_metadata', type: 'jsonb', nullable: true })
  extraMetadata: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // 🔗 Relations
  @OneToMany(() => PrivateMessage, (msg) => msg.file)
  privateMessages: PrivateMessage[];

  @OneToMany(() => GroupMessage, (msg) => msg.file)
  groupMessages: GroupMessage[];

  @OneToMany(() => MediaEncryptionKey, (key) => key.media)
  encryptionKeys: MediaEncryptionKey[];
}
