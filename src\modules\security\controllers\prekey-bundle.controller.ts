import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PreKeyBundleService } from '../services/prekey-bundleService';
import { OneTimePreKeyArrayDto } from '../dtos/onetime-prekey.dto';
import { CreateSignedPreKeyDto } from '../dtos/create-signed-prekey.dto';

@ApiTags('PreKey Bundle')
@Controller('prekey-bundle/:memberId')
export class PreKeyBundleController {
  constructor(private readonly preKeyBundleService: PreKeyBundleService) {}

  @Get('signed-prekeys')
  @ApiOperation({ summary: 'Get all signed prekeys for a member' })
  @ApiParam({ name: 'memberId', type: 'number', description: 'OrgMember ID' })
  @ApiResponse({ status: 200, description: 'List of signed prekeys returned' })
  async findAllSignedPreKeys(
    @Param('memberId', ParseIntPipe) memberId: number,
  ) {
    return this.preKeyBundleService.findAllSignedPreKeys(memberId);
  }

  @Post('one-time-prekeys')
  @ApiOperation({ summary: 'Upload an array of one-time prekeys for a member' })
  @ApiParam({ name: 'memberId', type: 'number', description: 'OrgMember ID' })
  async uploadOneTimePreKeys(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Body() body: OneTimePreKeyArrayDto,
  ) {
    return this.preKeyBundleService.saveOneTimePreKeys(memberId, body);
  }

  @Get()
  @ApiOperation({ summary: 'Get a specific prekey bundle for a member' })
  @ApiParam({ name: 'memberId', type: 'number', description: 'OrgMember ID' })
  @ApiResponse({ status: 200, description: 'Prekey bundle found' })
  @ApiResponse({ status: 404, description: 'Prekey bundle not found' })
  async findOneSignedPreKey(@Param('memberId', ParseIntPipe) memberId: number) {
    return this.preKeyBundleService.getPreKeyBundle(memberId);
  }

  @Post('signed-prekey')
  @ApiOperation({ summary: 'Create/Upload new signed prekey for member' })
  @ApiResponse({ status: 201, description: 'Signed prekey created' })
  async createSignedPreKey(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Body() createDto: CreateSignedPreKeyDto,
  ) {
    return this.preKeyBundleService.createSignedPreKey(memberId, createDto);
  }
}
