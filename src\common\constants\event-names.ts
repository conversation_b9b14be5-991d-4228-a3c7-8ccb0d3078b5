export const EVENT_NAMES = {
  OTP_REQUESTED: 'otp.requested',
  MEMBER_VERIFIED: 'member.verified',
  MESSAGE_CREATED: 'message.created',
  CIRCUIT_BREAKER_OPENED: 'circuit-breaker.opened',
  CIR<PERSON>IT_BREAKER_HALF_OPEN: 'circuitBreaker.halfOpen',
  CIRCUIT_BREAKER_CLOSED: 'circuit-breaker.closed',
  PRESENCE_UPDATED: 'presence.updated',
  PRESENCE_NOTIFY: 'presence.notify',
  MEMBER_CONNECTED: 'member.connected',
  JOIN_PRIVATE_ROOM: 'join.private.room',
  JOIN_GROUP_ROOM: 'join.group.room',
  JOIN_ASSOCIATED_ROOM: 'join.associated.room',

  // Chat Operation Request Events (Socket -> Feature Modules)
  SEND_MESSAGE_REQUESTED: 'chat.send_message.requested',
  MESSAGE_READ_REQUESTED: 'chat.message_read.requested',
  DELETE_MESSAGE_FOR_ME_REQUESTED: 'chat.delete_message_for_me.requested',
  DELETE_MESSAGES_FOR_EVERYONE_REQUESTED:
    'chat.delete_messages_for_everyone.requested',
  USER_TYPING: 'chat.user_typing',

  // Chat Operation Response Events (Feature Modules -> Socket)
  MESSAGE_SENT: 'chat.message.sent',
  MESSAGE_READ: 'chat.message.read',
  MESSAGE_DELETED: 'chat.message.deleted',

  MESSAGE_DELIVERY_ATTEMPT: 'message.delivery.attempt',
  MESSAGE_DELIVERY_FAILED: 'message.delivery.failed',
  MESSAGE_DELIVERY_SUCCESS: 'message.delivery.success',

  NEW_GROUP_MESSAGE: 'new_group_message',

  // Room Events
  MEMBER_JOINED_CHAT: 'room.member_joined',
  MEMBER_LEFT_CHAT: 'room.member_left',

  GROUP_INFO: 'group.info',
  GROUP_UPDATE: 'group.update',
  GROUP_META_UPDATE: 'group.meta_update',

  // System Events
  HEALTH_CHECK: 'system.health_check',
  DEBUG_INFO: 'system.debug_info',
};

export type EventName = (typeof EVENT_NAMES)[keyof typeof EVENT_NAMES];
