import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { SignedPreKeyEntity } from '../entities/signed-prekey.entity';
import { OneTimePreKeyEntity } from '../entities/one-time-prekey.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { OneTimePreKeyArrayDto } from '../dtos/onetime-prekey.dto';
import { CreatePreKeyBundleDto } from '../dtos/prekey-bundle.dto';
import { CreateSignedPreKeyDto } from '../dtos/create-signed-prekey.dto';

export interface OneTimePreKeyInterface {
  keyId: number;
  publicKey: string;
}

@Injectable()
export class PreKeyBundleService {
  constructor(
    @InjectRepository(SignedPreKeyEntity)
    private readonly signedPreKeyRepo: Repository<SignedPreKeyEntity>,

    @InjectRepository(OneTimePreKeyEntity)
    private readonly oneTimePreKeyRepo: Repository<OneTimePreKeyEntity>,
  ) {}

  async saveOneTimePreKeys(memberId: number, dto: OneTimePreKeyArrayDto) {
    const entities = dto.keys.map((key) =>
      this.oneTimePreKeyRepo.create({
        memberId,
        keyId: key.keyId,
        publicKey: key.publicKey,
        isUsed: false,
      }),
    );

    return await this.oneTimePreKeyRepo.save(entities);
  }

  async createPreKeyBundle(memberId: number, dto: CreatePreKeyBundleDto) {
    // 1. Save the signed prekey
    const signedPreKey = this.signedPreKeyRepo.create({
      memberId,
      keyId: dto.keyId,
      publicKey: dto.publicKey,
      signature: dto.signature,
      expireAt: dto.expireAt,
      isActive: true,
    });
    await this.signedPreKeyRepo.save(signedPreKey);

    // 2. Save the provided one-time prekeys
    const oneTimePreKeys = dto.oneTimePreKeys.map((k) =>
      this.oneTimePreKeyRepo.create({
        memberId,
        keyId: k.keyId,
        publicKey: k.publicKey,
        isUsed: false,
      }),
    );
    await this.oneTimePreKeyRepo.save(oneTimePreKeys);

    // 3. Return the full prekey bundle
    return {
      signedPreKeyId: signedPreKey.id,
      keyId: signedPreKey.keyId,
      publicKey: signedPreKey.publicKey,
      signature: signedPreKey.signature,
      expireAt: signedPreKey.expireAt,
      oneTimePreKeys: oneTimePreKeys.map((k) => ({
        keyId: k.keyId,
        publicKey: k.publicKey,
      })),
    };
  }

  async createSignedPreKey(memberId: number, dto: CreateSignedPreKeyDto) {
    await this.signedPreKeyRepo.delete({ member: { id: memberId } });

    // Save new signed prekey
    const newKey = this.signedPreKeyRepo.create({
      ...dto,
      member: { id: memberId } as any,
    });
    return this.signedPreKeyRepo.save(newKey);
  }

  async findAllSignedPreKeys(memberId: number) {
    return this.signedPreKeyRepo.find({ where: { memberId } });
  }

  async getPreKeyBundle(memberId: number) {
    const now = new Date();
    // 1. Get latest unexpired signed prekey
    const signedPreKey = await this.signedPreKeyRepo.findOne({
      where: { memberId, expireAt: MoreThan(now) },
      order: { createdAt: 'DESC' },
    });

    if (!signedPreKey) {
      throw new NotFoundException('No valid (unexpired) signed prekey found');
    }

    // 2. Try to get one unused one-time prekey (optional)
    let oneTimePreKey = await this.oneTimePreKeyRepo.findOne({
      where: { memberId, isUsed: false },
      order: { createdAt: 'ASC' },
    });

    // 3. Delete one-time prekey if found
    let oneTimePreKeyResponse: OneTimePreKeyInterface | null = null;
    if (oneTimePreKey) {
      oneTimePreKeyResponse = {
        keyId: oneTimePreKey.keyId,
        publicKey: oneTimePreKey.publicKey,
      };

      oneTimePreKey.isUsed = true;
      await this.oneTimePreKeyRepo.save(oneTimePreKey);
    }

    // 4. Fetch member with identityKey relation
    const member = await this.signedPreKeyRepo.manager.findOne(OrgMember, {
      where: { id: Number(memberId) },
      relations: ['identityKey'],
    });

    if (!member || !member.identityKey?.publicKey) {
      throw new NotFoundException('Member identity public key not found');
    }

    return {
      identityKey: member.identityKey.publicKey,
      signedPreKey: {
        keyId: signedPreKey.keyId,
        publicKey: signedPreKey.publicKey,
        signature: signedPreKey.signature,
        expireAt: signedPreKey.expireAt,
      },
      ...(oneTimePreKeyResponse && { oneTimePreKey: oneTimePreKeyResponse }),
    };
  }

  async deleteKeysByMemberId(memberId: number): Promise<void> {
    // Delete all signed prekeys for the member
    await this.signedPreKeyRepo.delete({ memberId });
    // Delete all OTP keys for the member
    await this.oneTimePreKeyRepo.delete({ memberId });
  }
}
