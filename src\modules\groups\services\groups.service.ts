import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In, QueryRunner } from 'typeorm';
import { Group } from '../entities/group.entity';
import { GroupMember } from '../entities/group-member.entity';
import { CreateGroupDto } from '../dto/create-group.dto';
import { UpdateGroupDto } from '../dto/update-group.dto';
import { StorageService } from '../../../core/storage/storage.service';
import { UsersService } from '../../users/services/users.service';
import { OrganizationsService } from '../../organization/services/organizations.service';
import { GroupLogService } from '../../audit/services/group.log.service';
import {
  GroupChangeType,
  GroupLogAction,
} from '../../audit/enums/group-log-action.enum';
import { MembersService } from 'src/modules/members/services/members.service';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';
import { MemberSyncService } from 'src/modules/members/services/member-sync.service';
import { GroupMembersService } from './group-member.service';
import { MemberEventType } from 'src/modules/audit/enums/member-event.enum';

@Injectable()
export class GroupsService {
  private readonly logger = new Logger(GroupsService.name);
  constructor(
    @InjectRepository(Group)
    private groupRepository: Repository<Group>,
    @InjectRepository(GroupMember)
    private groupMemberRepository: Repository<GroupMember>,
    private memberService: MembersService,
    private readonly userService: UsersService,
    private readonly organizationsService: OrganizationsService,
    private dataSource: DataSource,
    private readonly groupLogService: GroupLogService,
    private readonly storageService: StorageService,
    private readonly notificationService: NotificationService,
    private readonly memberSyncService: MemberSyncService,
    private readonly groupMemberService: GroupMembersService,
  ) {}

  /**
   * Create a new group with the creator as the first member
   */
  async create(createGroupDto: CreateGroupDto, userId: number): Promise<Group> {
    // Verify creator exists
    const creator = await this.userService.findById(userId);

    if (creator.roleId === 1) {
      throw new BadRequestException(
        'Product admin is not allowed to create organization members',
      );
    }

    const existingGroupname = await this.groupRepository.findOne({
      where: { name: createGroupDto.name, orgId: creator.orgId },
    });

    if (existingGroupname) {
      throw new ConflictException('Group with this name already exists');
    }

    // Verify that the organization exists
    const organization = await this.organizationsService.findOne(creator.orgId);

    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${creator.orgId} not found`,
      );
    }

    // Create group with transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the group
      const group = this.groupRepository.create({
        name: createGroupDto.name,
        description: createGroupDto.description,
        orgId: creator.orgId,
        imageUrl: createGroupDto.fileUrl,
        createdBy: userId,
        currentKeyVersion: 1,
      });

      const savedGroup = await queryRunner.manager.save(group);

      await queryRunner.commitTransaction();

      return this.findOne(savedGroup.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateGroup(
    id: number,
    updateGroupDto: UpdateGroupDto,
    userId: number,
  ): Promise<Group> {
    const group = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
      relations: ['organization'], // Add relations needed for notifications
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    const groupmembers =
      await this.groupMemberService.getActiveMembersWithDetails(id);
    if (groupmembers.length !== 0) {
      groupmembers.forEach(async (member) => {
        await this.memberSyncService.saveEvent({
          memberId: member.member.id,
          eventType: MemberEventType.GROUP_UPDATE,
          sourceTable: 'group',
          sourceId: id.toString(),
        });
      });
    }

    const oldGroup = { ...group }; // copy for comparison

    // Normalize old and new values
    const oldIsActive = !oldGroup.deletedAt;
    const newIsActive = !!updateGroupDto.isActive;

    const oldImageUrl = oldGroup.imageUrl || '';
    const newImageUrl = updateGroupDto.fileUrl || '';

    const oldDescription = oldGroup.description || '';
    const newDescription = updateGroupDto.description || '';

    // Track if we need to emit updates
    let shouldEmitUpdates = false;
    const changes: {
      name?: { old: string; new: string };
      imageUrl?: { old: string; new: string };
      description?: { old: string; new: string };
      isActive?: { old: boolean; new: boolean };
    } = {};

    // Update basic fields if changed (except isActive handled separately)
    const updates: Partial<Group> = {};

    if (oldGroup.name !== updateGroupDto.name) {
      updates.name = updateGroupDto.name;
      changes.name = {
        old: oldGroup.name ?? '',
        new: updateGroupDto.name ?? '',
      };
      shouldEmitUpdates = true;
    }

    if (oldImageUrl !== newImageUrl) {
      updates.imageUrl = newImageUrl;
      changes.imageUrl = { old: oldImageUrl, new: newImageUrl };
      shouldEmitUpdates = true;
    }

    if (oldDescription !== newDescription) {
      updates.description = newDescription;
      changes.description = { old: oldDescription, new: newDescription };
      shouldEmitUpdates = true;
    }

    if (Object.keys(updates).length > 0) {
      await this.groupRepository.update(id, updates);

      // Log metadata updates
      if (updates.name !== undefined) {
        const nextSeq = await this.groupLogService.logChange(
          id,
          GroupChangeType.META,
          GroupLogAction.UPDATE,
          {
            changedField: 'name',
            oldValue: oldGroup.name,
            newValue: updateGroupDto.name,
          },
        );
        await this.groupRepository.update(id, { lastSeq: nextSeq });
      }

      if (updates.imageUrl !== undefined) {
        const nextSeq = await this.groupLogService.logChange(
          id,
          GroupChangeType.META,
          GroupLogAction.UPDATE,
          {
            changedField: 'imageUrl',
            oldValue: oldImageUrl,
            newValue: newImageUrl,
          },
        );
        await this.groupRepository.update(id, { lastSeq: nextSeq });
      }

      if (updates.description !== undefined) {
        const nextSeq = await this.groupLogService.logChange(
          id,
          GroupChangeType.META,
          GroupLogAction.UPDATE,
          {
            changedField: 'description',
            oldValue: oldDescription,
            newValue: newDescription,
          },
        );
        await this.groupRepository.update(id, { lastSeq: nextSeq });
      }
    }

    // Handle isActive toggle only if changed
    if (oldIsActive !== newIsActive) {
      shouldEmitUpdates = true;
      changes.isActive = { old: oldIsActive, new: newIsActive };

      if (!newIsActive) {
        await this.groupRepository.update(id, {
          deletedAt: new Date(),
          deletedBy: userId,
        });

        const nextSeq = await this.groupLogService.logChange(
          id,
          GroupChangeType.META,
          GroupLogAction.DELETE,
          {
            changedField: 'isActive',
            oldValue: oldIsActive.toString(),
            newValue: newIsActive.toString(),
          },
        );
        await this.groupRepository.update(id, { lastSeq: nextSeq });
      } else {
        await this.groupRepository.update(id, {
          deletedAt: null as any,
          deletedBy: null as any,
        });

        const nextSeq = await this.groupLogService.logChange(
          id,
          GroupChangeType.META,
          GroupLogAction.UPDATE,
          {
            changedField: 'isActive',
            oldValue: oldIsActive.toString(),
            newValue: newIsActive.toString(),
          },
        );
        await this.groupRepository.update(id, { lastSeq: nextSeq });
      }
    }

    // Reload updated group including soft-deleted if needed
    const updatedGroup = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!updatedGroup) {
      throw new NotFoundException(`Group with ID ${id} not found after update`);
    }

    // Emit updates to verified members if there were changes
    if (shouldEmitUpdates) {
      try {
        // Get the latest sequence number after all logs have been added
        // const latestLog = await this.groupLogService.getLatestLog(id);
        // const latestSeq = latestLog?.seq || 0;

        // Get all active verified members
        const activeMembers = await this.groupMemberRepository.find({
          where: { groupId: id, isActive: true },
        });
        const memberIds = activeMembers.map((m) => m.memberId);

        if (memberIds.length > 0) {
          const verifiedMemberIds =
            await this.memberService.getVerifiedMemberIds(memberIds);

          if (verifiedMemberIds.length > 0) {
            // Get signed URL for new image if it changed
            const signedImageUrl = changes.imageUrl
              ? await this.storageService.generateSignedUrl(
                  updatedGroup.imageUrl,
                  315360000,
                )
              : null;

            await this.emitGroupMetaUpdates(updatedGroup, verifiedMemberIds, {
              changes,
              // latestSeq,
              signedImageUrl,
            });
          }
        }
      } catch (emitError) {
        this.logger.error('Failed to emit group metadata updates', emitError);
      }
    }

    return updatedGroup;
  }

  /**
   * Find a group by ID with related entities
   */
  async findOne(id: number): Promise<any> {
    const group = await this.groupRepository.findOne({
      where: { id },
      relations: ['organization', 'creator'],
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    const { password, username, ...creatorWithoutSensitive } =
      group.creator ?? {};

    return {
      ...group,
      creator: creatorWithoutSensitive,
    };
  }

  async findGroupWithRelations(
    groupId: number,
    queryRunner?: QueryRunner,
  ): Promise<any> {
    const manager = queryRunner?.manager || this.dataSource.manager;

    return await manager.findOne('Group', {
      where: { id: groupId },
      relations: ['organization', 'creator'],
    });
  }

  async deleteGroup(id: number, userId: number): Promise<void> {
    const group = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    if (group.deletedAt) {
      throw new BadRequestException(`Group with ID ${id} is already deleted`);
    }

    if (group.createdBy !== userId) {
      throw new ForbiddenException(
        `You are not authorized to delete this group`,
      );
    }

    await this.groupRepository.update(id, {
      deletedAt: new Date(),
      deletedBy: userId,
    });
    const activeMembers = await this.groupMemberRepository.find({
      where: { groupId: id, isActive: true },
    });

    const memberIds = activeMembers.map((m) => m.memberId);

    // 📣 Broadcast deletion to each member
    for (const memberId of memberIds) {
      // this.socketGateway.broadcastToMember(memberId, 'group_deleted', {
      //   groupId: id,
      // });
    }
  }

  async findGroupWithMembers(groupId: number): Promise<Group> {
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });

    if (!group) {
      throw new NotFoundException(`Group with id ${groupId} not found`);
    }

    return group;
  }

  async getGroupMemberIds(groupId: number): Promise<number[]> {
    const memberIds = await this.groupRepository
      .createQueryBuilder('group')
      .leftJoin('group.members', 'groupMember')
      .leftJoin('groupMember.member', 'member')
      .where('group.id = :groupId', { groupId })
      .andWhere('groupMember.isActive = :isActive', { isActive: true })
      .select('member.id', 'id')
      .getRawMany();

    if (!memberIds?.length) {
      throw new NotFoundException(
        `No verified & active members found for group ${groupId}`,
      );
    }

    return memberIds.map((m) => m.id);
  }

  async getVerifiedGroupMemberIds(groupId: number): Promise<number[]> {
    const memberIds = await this.groupRepository
      .createQueryBuilder('group')
      .leftJoin('group.members', 'groupMember')
      .leftJoin('groupMember.member', 'member')
      .where('group.id = :groupId', { groupId })
      .andWhere('groupMember.isActive = :isActive', { isActive: true })
      .andWhere('member.isVerified = :isVerified', { isVerified: true })
      .select('member.id', 'id')
      .getRawMany();

    if (!memberIds?.length) {
      throw new NotFoundException(
        `No verified & active members found for group ${groupId}`,
      );
    }

    return memberIds.map((m) => m.id);
  }

  async emitGroupMetaUpdates(
    group: Group,
    memberIds: number[],
    options: {
      changes: {
        name?: { old: string; new: string };
        imageUrl?: { old: string; new: string };
        description?: { old: string; new: string };
        isActive?: { old: boolean; new: boolean };
      };
      // latestSeq: number;
      signedImageUrl?: string | null;
    },
  ) {
    // Prepare base payload
    const payload = {
      groupId: group.id,
      type: 'group',
      timestamp: new Date(),
      // latestSeq: options.latestSeq,
      changes: options.changes,
      // Include current values
      current: {
        name: group.name,
        description: group.description,
        imageUrl: options.signedImageUrl || group.imageUrl,
        isActive: !group.deletedAt,
      },
    };

    // Emit to each verified member
    for (const memberId of memberIds) {
      try {
        await this.notificationService.broadcastToMemberWithAck(
          memberId,
          EVENT_NAMES.GROUP_META_UPDATE,
          payload,
        );
      } catch (error) {
        this.logger.error(
          `Failed to emit meta update to member ${memberId}`,
          error,
        );
      }
    }
  }

  // async allocateGroupMembers(
  //   dto: AllocateMemberDto,
  //   userId: string,
  // ): Promise<GroupMember[]> {
  //   const appName = this.configService.get<string>('APP_NAME', 'ChatApp');
  //   const { groupId, memberIds: targetMemberIds, adminSecretKey } = dto;

  //   if (!adminSecretKey) {
  //     throw new Error('Admin secret key is required for encryption operations');
  //   }

  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {
  //     // Validate group and user authorization
  //     const group = await queryRunner.manager.findOne(Group, {
  //       where: { id: groupId },
  //       relations: ['organization', 'creator'],
  //     });

  //     if (!group)
  //       throw new NotFoundException(`Group with ID ${groupId} not found`);
  //     if (Number(group.createdBy) !== Number(userId)) {
  //       throw new ForbiddenException(`Not authorized to allocate members`);
  //     }

  //     // Get existing members and calculate delta
  //     const existingGroupMembers = await queryRunner.manager.find(GroupMember, {
  //       where: { groupId, isActive: true },
  //     });
  //     const existingMemberIds = existingGroupMembers.map((m) => m.memberId);

  //     const membersToAdd = targetMemberIds.filter(
  //       (id) => !existingMemberIds.includes(id),
  //     );
  //     const membersToRemove = existingMemberIds.filter(
  //       (id) => !targetMemberIds.includes(id),
  //     );

  //     // Process removals
  //     for (const memberId of membersToRemove) {
  //       await queryRunner.manager.update(
  //         GroupMember,
  //         { groupId, memberId, isActive: true },
  //         { isActive: false, leftAt: new Date() },
  //       );

  //       await this.groupLogService.logChange(
  //         groupId,
  //         GroupChangeType.MEMBER,
  //         GroupLogAction.LEAVE,
  //         { memberId },
  //       );
  //     }

  //     // Process additions (without notifications yet)
  //     const addedMembers: GroupMember[] = [];
  //     const newMemberDetails: {
  //       id: number;
  //       email: string;
  //       name: string;
  //       phoneNo: string;
  //       isVerified: boolean;
  //     }[] = [];

  //     for (const memberId of membersToAdd) {
  //       const member = await queryRunner.manager.findOne(OrgMember, {
  //         where: { id: memberId },
  //         select: ['id', 'email', 'name', 'phoneNo', 'isVerified'],
  //       });
  //       if (!member) continue;

  //       const newMembership = await queryRunner.manager.save(GroupMember, {
  //         groupId,
  //         memberId,
  //         joinedAt: new Date(),
  //       });

  //       addedMembers.push(newMembership);
  //       newMemberDetails.push({
  //         ...member,
  //         isVerified: member.isVerified,
  //       });

  //       await this.groupLogService.logChange(
  //         groupId,
  //         GroupChangeType.MEMBER,
  //         GroupLogAction.JOIN,
  //         { memberId },
  //       );
  //     }

  //     const oldKeyVersion = group.currentKeyVersion;
  //     // Key rotation for added members
  //     let newKeyVersion: number | null = null;
  //     if (membersToAdd.length > 0) {
  //       const existingActiveMemberIds = existingMemberIds.filter(
  //         (id) => !membersToRemove.includes(id),
  //       );

  //       newKeyVersion =
  //         await this.groupEncryptionService.allocateMembersWithKeyRotation(
  //           groupId,
  //           adminSecretKey,
  //           membersToAdd,
  //           existingActiveMemberIds,
  //         );

  //       // Log key version changes for ALL affected members (existing + new)
  //       const allAffectedMemberIds = [
  //         ...existingActiveMemberIds,
  //         ...membersToAdd,
  //       ];

  //       for (const memberId of allAffectedMemberIds) {
  //         await this.groupLogService.logChange(
  //           groupId,
  //           GroupChangeType.KEY,
  //           GroupLogAction.KEY_UPDATE,
  //           {
  //             memberId,
  //             changedField: 'key_version',
  //             oldValue: oldKeyVersion.toString(),
  //             newValue: newKeyVersion.toString(),
  //           },
  //         );
  //       }
  //     }

  //     // Commit transaction first before any external communications
  //     await queryRunner.commitTransaction();

  //     // Get updated member list
  //     const updatedGroupMembers = await this.groupMemberRepository.find({
  //       where: { groupId, isActive: true },
  //     });

  //     // Now safe to send notifications and emit events
  //     try {
  //       // Send email notifications
  //       for (const member of newMemberDetails) {
  //         if (member.email && !member.isVerified) {
  //           await this.mailService.sendMemberNotification(
  //             member.email,
  //             member.name,
  //             group.organization.name,
  //             group.name,
  //             appName,
  //             member.phoneNo,
  //           );
  //         }
  //       }

  //       // Emit events to all relevant parties
  //       if (membersToAdd.length > 0 || membersToRemove.length > 0) {
  //         const latestLog = await this.groupLogService.getLatestLog(groupId);
  //         const latestSeq = latestLog?.seq || 0;

  //         // Get verified members only once
  //         const allRelevantMemberIds = [...membersToAdd, ...membersToRemove];
  //         const verifiedMembers =
  //           await this.memberService.getVerifiedMembersWithSignedUrls(
  //             allRelevantMemberIds,
  //           );

  //         // 1. Emit to new members (full group info)
  //         if (membersToAdd.length > 0) {
  //           const verifiedNewMemberIds = verifiedMembers
  //             .filter((m) => membersToAdd.includes(m.id))
  //             .map((m) => m.id);

  //           if (verifiedNewMemberIds.length > 0) {
  //             await this.groupNotificationService.emitGroupInfoToNewMembers(
  //               group,
  //               verifiedNewMemberIds,
  //               verifiedMembers,
  //               latestSeq,
  //             );
  //           }
  //         }

  //         // 2. Emit to existing members about changes
  //         const existingActiveMemberIds = existingMemberIds.filter(
  //           (id) => !membersToRemove.includes(id),
  //         );

  //         if (existingActiveMemberIds.length > 0) {
  //           const verifiedExistingMemberIds =
  //             await this.memberService.getVerifiedMemberIds(
  //               existingActiveMemberIds,
  //             );

  //           if (verifiedExistingMemberIds.length > 0) {
  //             await this.groupNotificationService.emitGroupUpdatesToExistingMembers(
  //               group,
  //               verifiedExistingMemberIds,
  //               {
  //                 newMembers: verifiedMembers.filter((m) =>
  //                   membersToAdd.includes(m.id),
  //                 ),
  //                 leftMembers: verifiedMembers.filter((m) =>
  //                   membersToRemove.includes(m.id),
  //                 ),
  //                 newKeyVersion,
  //               },
  //               latestSeq,
  //             );
  //           }
  //         }
  //       }
  //     } catch (notificationError) {
  //       this.logger.error('Failed to send notifications', notificationError);
  //     }

  //     return updatedGroupMembers;
  //   } catch (error) {
  //     await queryRunner.rollbackTransaction();
  //     throw error;
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  async findAllByOrgId(orgId: number): Promise<any[]> {
    const groups = await this.groupRepository.find({
      where: { orgId },
      relations: ['members'],
      withDeleted: true,
    });

    const groupData = groups.map((group) => {
      const result = { ...group, isActive: group.deletedAt == null };
      return result;
    });

    return groupData;
  }

  async getAllGroupsWithMembers(orgId: number) {
    const groups = await this.groupRepository.find({
      where: { orgId, isActive: true },
      relations: ['members', 'members.member'],
    });

    return groups
      .filter((group) => group.members?.some((gm) => gm.isActive && gm.member))
      .map((group) => ({
        id: group.id,
        groupId: group.id,
        groupName: group.name,
        createdAt: group.createdAt.toISOString().split('T')[0],
        members: group.members
          .filter((gm) => gm.isActive && gm.member)
          .map((gm) => ({
            memberId: gm.member?.id,
            name: gm.member?.name || 'Unknown',
            imageUrl: gm.member?.imageUrl,
            joiningDate: gm.joinedAt.toISOString().split('T')[0],
          })),
      }));
  }
}
