import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DoubleRatchetService } from './services/double-ratchet.service';
import { KeyDerivationService } from './services/key-derivation.service';
import { KeyRotationService } from './services/key-rotation.service';
import { PrekeyManagementService } from './services/prekey-management.service';
import { SessionStateService } from './services/session-state.service';
import { SignalProtocolService } from './services/signal-protocol.service';
import { SignatureService } from './services/signature.service';
import { X3dhService } from './services/x3dh.service';
import { EncryptionModule } from '../../core/encryption/encryption.module';
import { SocketModule } from 'src/infrastructure/socket/socket.module';
import { KeyExchangeService } from './services/key-exchange.service';
import { MessageEncryptionService } from './services/message-encryption.service';
import { SessionEstablishmentService } from './services/session-establishment.service';
import { SignalMessagingService } from './services/signal-messaging.service';
import { IdentityKeyService } from './services/identity-key.service';
import { IdentityKeyEntity } from './entities/identity-key.entity';
import { ChainKeyStateEntity } from './entities/chain-key-states.entity';
import { OneTimePreKeyEntity } from './entities/one-time-prekey.entity';
import { SessionStateEntity } from './entities/session-state.entity';
import { SignedPreKeyEntity } from './entities/signed-prekey.entity';
import { PreKeyBundleService } from './services/prekey-bundleService';
import { PreKeyBundleController } from './controllers/prekey-bundle.controller';

/**
 * Security Module
 *
 * Handles cryptographic operations, key management,
 * Signal Protocol implementation, and security-related
 * services for end-to-end encryption.
 */
@Module({
  imports: [
    EncryptionModule,
    SocketModule,
    TypeOrmModule.forFeature([
      IdentityKeyEntity,
      ChainKeyStateEntity,
      OneTimePreKeyEntity,
      SessionStateEntity,
      SignedPreKeyEntity,
    ]),
  ],
  controllers: [PreKeyBundleController],
  providers: [
    IdentityKeyService,
    DoubleRatchetService,
    KeyDerivationService,
    KeyRotationService,
    PrekeyManagementService,
    PreKeyBundleService,
    SessionStateService,
    SignalProtocolService,
    SignatureService,
    X3dhService,
    KeyExchangeService,
    MessageEncryptionService,
    SessionEstablishmentService,
    SignalMessagingService,
  ],
  exports: [
    IdentityKeyService,
    DoubleRatchetService,
    KeyDerivationService,
    KeyRotationService,
    PrekeyManagementService,
    SessionStateService,
    SignalProtocolService,
    SignatureService,
    PreKeyBundleService,
    X3dhService,
    KeyExchangeService,
    MessageEncryptionService,
    SessionEstablishmentService,
    SignalMessagingService,
  ],
})
export class SecurityModule {}
