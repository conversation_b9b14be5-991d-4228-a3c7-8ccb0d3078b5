import { Injectable, NotFoundException } from '@nestjs/common';
import { MoreThanOrEqual, Query<PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import { PrivateMessage } from '../entities/private-message.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SavePrivateMessageDto } from '../dto/save-private-message.dto';

@Injectable()
export class PrivateMessageService {
  constructor(
    @InjectRepository(PrivateMessage)
    private readonly privateMessageRepo: Repository<PrivateMessage>,
  ) {}

  /**
   * Save a new private message
   */
  async saveMessage(dto: SavePrivateMessageDto): Promise<PrivateMessage> {
    const message = this.privateMessageRepo.create({
      senderId: dto.senderId,
      receiverId: dto.receiverId,
      encryptedContent: dto.encryptedContent,
      ephemeralPublicKey: dto.ephemeralPublicKey,
      messageIndex: dto.messageIndex,
      previousChainLength: dto.previousChainLength,
      chainKeyVersion: dto.chainKeyVersion,
      replyToMessageId: dto.replyToMessageId,
      caption: dto.caption,
      file: dto.fileId ? ({ id: dto.fileId } as any) : undefined,
      sentAt: dto.sentAt || new Date(),
    });

    const savedMessage = await this.privateMessageRepo.save(message);

    // Fetch the complete message with relations
    const completeMessage = await this.privateMessageRepo.findOne({
      where: { id: savedMessage.id },
      relations: ['sender', 'receiver', 'file'],
    });

    if (!completeMessage) {
      throw new NotFoundException('Message not found after saving');
    }

    return completeMessage;
  }

  /**1
   * Mark a message as delivered
   */
  async markDelivered(messageId: number): Promise<void> {
    await this.privateMessageRepo.update(messageId, {
      deliveredAt: new Date(),
    });
  }

  /**
   * Mark a message as acknowledged (read)
   */
  async markAcknowledged(messageId: number): Promise<void> {
    await this.privateMessageRepo.update(messageId, {
      acknowledged: true,
    });
  }

  /**
   * Mark multiple messages as acknowledged
   */
  async markMultipleAcknowledged(messageIds: number[]): Promise<void> {
    if (messageIds.length === 0) return;

    await this.privateMessageRepo
      .createQueryBuilder()
      .update(PrivateMessage)
      .set({ acknowledged: true })
      .whereInIds(messageIds)
      .execute();
  }

  /**
   * Get message history between two members
   */
  async getMessageHistory(
    senderId: number,
    receiverId: number,
    limit: number = 50,
    beforeMessageId?: number,
  ): Promise<PrivateMessage[]> {
    const queryBuilder = this.privateMessageRepo
      .createQueryBuilder('pm')
      .leftJoinAndSelect('pm.sender', 'sender')
      .leftJoinAndSelect('pm.receiver', 'receiver')
      .leftJoinAndSelect('pm.file', 'file')
      .where(
        '(pm.senderId = :senderId AND pm.receiverId = :receiverId) OR (pm.senderId = :receiverId AND pm.receiverId = :senderId)',
        { senderId, receiverId },
      )
      .orderBy('pm.sentAt', 'DESC')
      .take(limit);

    if (beforeMessageId) {
      const beforeMessage = await this.privateMessageRepo.findOne({
        where: { id: beforeMessageId },
      });
      if (beforeMessage) {
        queryBuilder.andWhere('pm.sentAt < :beforeDate', {
          beforeDate: beforeMessage.sentAt,
        });
      }
    }

    return queryBuilder.getMany();
  }

  /**
   * Get unread message count for a member from another member
   */
  async getUnreadCount(receiverId: number, senderId: number): Promise<number> {
    return this.privateMessageRepo.count({
      where: {
        senderId,
        receiverId,
        acknowledged: false,
      },
    });
  }

  /**
   * Find a single message by ID
   */
  async findOneMessage(messageId: number): Promise<PrivateMessage> {
    const message = await this.privateMessageRepo.findOne({
      where: { id: messageId },
      relations: ['sender', 'receiver', 'file'],
    });

    if (!message) {
      throw new NotFoundException(`Message with ID ${messageId} not found`);
    }

    return message;
  }

  async createPrivateWelcomeMessage(
    senderId: number,
    receiverId: number,
    content: string,
    queryRunner: QueryRunner,
  ): Promise<any> {
    const welcomeMessage = queryRunner.manager.create(PrivateMessage, {
      senderId,
      receiverId,
      encryptedContent: content,
      messageIndex: 1,
      previousChainLength: 0,
      chainKeyVersion: 1,
      sentAt: new Date(),
      deliveredAt: new Date(),
      acknowledged: false,
    });

    const savedMessage = await queryRunner.manager.save(
      PrivateMessage,
      welcomeMessage,
    );

    return savedMessage;
  }

  async getLastMessageForThread(
    senderId: number,
    receiverId: number,
  ): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: [
        { senderId, receiverId },
        { senderId: receiverId, receiverId: senderId },
      ],
      relations: ['sender', 'receiver', 'file'],
      // order: { seq: 'DESC' },
    });
  }

  async findLastMessageAfterSession(
    senderId: number,
    receiverId: number,
    sessionCreatedAt: Date,
  ): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: [
        {
          senderId,
          receiverId,
          sentAt: MoreThanOrEqual(sessionCreatedAt),
        },
        {
          senderId: receiverId,
          receiverId: senderId,
          sentAt: MoreThanOrEqual(sessionCreatedAt),
        },
      ],
      order: { sentAt: 'DESC' },
      relations: ['sender', 'receiver', 'file'],
    });
  }
}
