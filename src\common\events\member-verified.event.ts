// src/common/events/member-verified.event.ts
import { BaseEvent } from './base.event';
import { EVENT_NAMES } from '../constants/event-names';

export class MemberVerifiedEvent extends BaseEvent {
  readonly name = EVENT_NAMES.MEMBER_VERIFIED;

  constructor(
    public readonly payload: {
      memberId: number;
      isVerified: boolean;
      orgId: number;
      timestamp: string;
    },
  ) {
    super();
  }
}
