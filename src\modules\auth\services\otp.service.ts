import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { OtpVerification } from '../entities/otp-verification.entity';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { ConfigService } from '@nestjs/config';
import { MembersService } from '../../members/services/members.service';
import { EventBusService } from '../../../infrastructure/events/event-bus.service';
import { OtpRequestedEvent } from '../../../common/events';
import { RequestOtpDto } from '../dto/request-otp.dto';
import { CacheService } from '../../../infrastructure/redis/services/cache.service';
import { Request } from 'express';
import { PubSubService } from 'src/infrastructure/redis/services/pubsub.service';
import { USER_TYPE } from 'src/common/constants/user-type.constants';
import { JwtOrgMemberPayload } from '../interfaces/jwt-payload.interface';
import { TokenService } from './token.service';
import { SessionManagementService } from 'src/modules/chat/p2p/services/session-management.service';

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);

  constructor(
    private readonly membersService: MembersService,
    @InjectRepository(OtpVerification)
    private otpVerificationRepository: Repository<OtpVerification>,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventBus: EventBusService,
    private readonly tokenService: TokenService,
    private readonly sessionManagementService: SessionManagementService,
    private readonly cacheService: CacheService,
    private readonly pubService: PubSubService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate a random 6-digit OTP
   */
  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Request OTP for login
   * @param phoneNumber User's phone number
   */
  async requestOtp(requestOtpDto: RequestOtpDto): Promise<{ message: string }> {
    // Find user
    const member = await this.membersService.findByPhoneNo(
      requestOtpDto.phoneNumber,
    );
    if (!member) {
      throw new BadRequestException('User not found');
    }

    if (!member.groupMemberships || member.groupMemberships.length === 0) {
      throw new BadRequestException(
        'You have not been allocated by your admin yet for any groups',
      );
    }

    // Check active devices for this member except current device
    await this.membersService.checkActiveSessionsOnOtherDevices(
      member.id,
      requestOtpDto.deviceId,
    );
    // Generate OTP
    const otp = this.generateOtp();

    // Calculate expiration (15 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);

    // Create OTP verification record
    const otpVerification = this.otpVerificationRepository.create({
      memberId: member.id,
      otpCode: otp,
      expiresAt,
      verificationAttempts: 0,
    });

    await this.otpVerificationRepository.save(otpVerification);

    // Send OTP via SMS
    try {
      this.eventBus.publish(
        new OtpRequestedEvent(requestOtpDto.phoneNumber, otp),
      );
      return { message: 'OTP sent successfully' };
    } catch (error) {
      this.logger.error(`Failed to send OTP SMS: ${error.message}`);
      throw new BadRequestException('Failed to send OTP');
    }
  }

  /**
   * Verify OTP and generate authentication token via AuthService
   * @param phoneNumber User's phone number
   * @param otpCode OTP code entered by user
   */
  async verifyOtp(verifyOtpDto: VerifyOtpDto, req: Request) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const member = await queryRunner.manager.findOne(OrgMember, {
        where: { phoneNo: verifyOtpDto.phoneNumber },
        relations: ['organization'],
      });

      if (!member) {
        throw new BadRequestException('User not found');
      }

      // Verify OTP
      await this.validateOtp(queryRunner, member.id, verifyOtpDto.otpCode);

      // Update member status
      await this.updateMemberAfterVerification(queryRunner, member);

      const payload: JwtOrgMemberPayload = {
        sub: member.id,
        type: USER_TYPE.ORG_MEMBER,
        orgId: member.orgId,
      };

      const tokens = await this.tokenService.generateTokens(payload);

      await this.tokenService.storeRefreshToken(
        USER_TYPE.ORG_MEMBER,
        member.id,
        tokens.refreshToken,
        tokens.tokenId,
        req,
      );

      // Store FCM token and set member online
      await Promise.all([
        this.cacheService.storeFcmToken(
          member.id,
          verifyOtpDto.fcmToken,
          verifyOtpDto.deviceId,
        ),
        this.pubService.setMemberOnline(member.id, verifyOtpDto.deviceId),
        this.membersService.upsertFcmToken(
          queryRunner,
          member.id,
          verifyOtpDto,
        ),
      ]);
      await this.sessionManagementService.reactivateMemberSessions(
        member.id,
        queryRunner.manager,
      );

      await queryRunner.commitTransaction();
      return tokens;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`OTP verification failed: ${error.message}`);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async validateOtp(
    queryRunner: QueryRunner,
    memberId: number,
    otpCode: string,
  ): Promise<void> {
    const otpVerification = await queryRunner.manager.findOne(OtpVerification, {
      where: { memberId },
      order: { id: 'DESC' },
    });

    if (!otpVerification) {
      throw new ForbiddenException('OTP verification required');
    }

    if (otpVerification.expiresAt < new Date()) {
      throw new ForbiddenException('OTP has expired');
    }

    if (
      otpVerification.verificationAttempts >=
      this.configService.get('otp.maxAttempts', 3)
    ) {
      throw new ForbiddenException('Maximum verification attempts reached');
    }

    // Increment attempts
    otpVerification.verificationAttempts += 1;

    if (otpVerification.otpCode !== otpCode) {
      await queryRunner.manager.save(otpVerification);
      throw new BadRequestException('Invalid OTP code');
    }

    // Mark as verified
    otpVerification.verifiedAt = new Date();
    await queryRunner.manager.save(otpVerification);
  }

  private async updateMemberAfterVerification(
    queryRunner: QueryRunner,
    member: OrgMember,
  ): Promise<void> {
    member.isVerified = true;
    member.lastLoginAt = new Date();
    await queryRunner.manager.save(member);
  }

  /**
   * Resend OTP verification code
   * @param phoneNumber User's phone number
   */
  async resendOtp(requestOtpDto: RequestOtpDto): Promise<{ message: string }> {
    // Generate new OTP
    return this.requestOtp(requestOtpDto);
  }

  async logout(memberId: number, deviceId: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // NEW: Deactivate sessions and handle private thread permissions
      await this.sessionManagementService.deactivateMemberSessions(
        memberId,
        queryRunner.manager,
      );

      await Promise.all([
        this.pubService.setMemberOffline(memberId, deviceId),
        this.cacheService.removeFcmToken(memberId, deviceId),
        this.membersService.deleteMemberPreKeyBundle(memberId),
        this.membersService.invalidToken(memberId, deviceId),
      ]);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Logout failed for member ${memberId}: ${error.message}`,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
