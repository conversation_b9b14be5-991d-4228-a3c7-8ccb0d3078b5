import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { MemberEventLog } from '../entities/member-event-logs.entity';
import { MemberEventType } from '../enums/member-event.enum';

@Injectable()
export class EventLogService {
  constructor(
    @InjectRepository(MemberEventLog)
    private readonly eventLogRepo: Repository<MemberEventLog>,
  ) {}

  /**
   * Get next sequence number for a member's events
   */
  async getNextMemberSeq(
    memberId: number,
    manager: EntityManager,
  ): Promise<number> {
    const result = await manager
      .createQueryBuilder(MemberEventLog, 'event')
      .select('MAX(event.memberSeq)', 'maxSeq')
      .where('event.memberId = :memberId', { memberId })
      .getRawOne();

    // Ensure numeric conversion
    return (parseInt(result?.maxSeq, 10) || 0) + 1;
  }

  /**
   * Create a new event log entry
   */
  async createEventLog(
    memberId: number,
    manager: EntityManager,
    eventType: MemberEventType,
    sourceTable?: string,
    sourceId?: string,
  ): Promise<MemberEventLog> {
    const seq = await this.getNextMemberSeq(memberId, manager);

    const eventLog = manager.create(MemberEventLog, {
      memberId,
      memberSeq: seq,
      eventType,
      sourceTable,
      sourceId,
    });

    return manager.save(eventLog);
  }
}
